package com.xinghuo.project.biz.model.bizContractMoney;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 合同收款表单对象
 */
@Data
@Schema(description = "合同收款表单对象")
public class BizContractMoneyForm {

    /**
     * 合同ID
     */
    @NotBlank(message = "合同ID不能为空")
    @Schema(description = "合同ID", required = true)
    private String cid;

    /**
     * 付款条件
     */
    @Schema(description = "付款条件")
    private String fktj;

    /**
     * 收款比例
     */
    @Schema(description = "收款比例")
    private String ratio;

    /**
     * 收款金额
     */
    @NotNull(message = "收款金额不能为空")
    @Schema(description = "收款金额", required = true)
    private BigDecimal cmMoney;

    /**
     * 项目经理
     */
    @Schema(description = "项目经理")
    private Long ownId;

    /**
     * 收款状态
     */
    @Schema(description = "收款状态")
    private String payStatus;

    /**
     * 开票日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(description = "开票日期")
    private Date kaipiaoDate;

    /**
     * 应收日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(description = "应收日期")
    private Date yingshouDate;

    /**
     * 预收日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(description = "预收日期")
    private Date yushouDate;

    /**
     * 收款日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(description = "收款日期")
    private Date shoukuanDate;

    /**
     * 临时保存预收日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(description = "临时保存预收日期")
    private Date tmpDate;

    /**
     * 最后备注
     */
    @Schema(description = "最后备注")
    private String lastNote;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 部门ID
     */
    @Schema(description = "部门ID")
    private String deptId;

    /**
     * 一部金额
     */
    @Schema(description = "一部金额")
    private BigDecimal ybAmount;

    /**
     * 二部金额
     */
    @Schema(description = "二部金额")
    private BigDecimal ebAmount;

    /**
     * 综合金额
     */
    @Schema(description = "综合金额")
    private BigDecimal otherAmount;
}
