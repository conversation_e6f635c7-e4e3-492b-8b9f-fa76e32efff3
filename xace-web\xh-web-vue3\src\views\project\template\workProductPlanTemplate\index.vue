<template>
  <div>
    <BasicTable @register="registerTable" :searchInfo="searchInfo">
      <template #toolbar>
        <a-button type="primary" @click="handleCreate">
          <Icon icon="ant-design:plus-outlined" /> 新建模板
        </a-button>
        <a-button @click="handleBatchDelete" :disabled="!selectedRowKeys.length">
          <Icon icon="ant-design:delete-outlined" /> 批量删除
        </a-button>
        <a-button @click="handleBatchPublish" :disabled="!selectedRowKeys.length">
          <Icon icon="ant-design:cloud-upload-outlined" /> 批量发布
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'clarity:note-edit-line',
                tooltip: '编辑',
                onClick: handleEdit.bind(null, record),
              },
              {
                icon: 'ant-design:eye-outlined',
                tooltip: '查看详情',
                onClick: handleView.bind(null, record),
              },
              {
                icon: 'ant-design:copy-outlined',
                tooltip: '复制',
                onClick: handleCopy.bind(null, record),
              },
              {
                icon: 'ant-design:cloud-upload-outlined',
                tooltip: '发布',
                ifShow: record.knStatusId === 'draft',
                onClick: handlePublish.bind(null, record),
              },
              {
                icon: 'ant-design:inbox-outlined',
                tooltip: '归档',
                ifShow: record.knStatusId === 'published',
                onClick: handleArchive.bind(null, record),
              },
              {
                icon: 'ant-design:delete-outlined',
                tooltip: '删除',
                color: 'error',
                popConfirm: {
                  title: '是否确认删除',
                  placement: 'left',
                  confirm: handleDelete.bind(null, record),
                },
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <WorkProductPlanTemplateModal @register="registerModal" @success="handleSuccess" />
    <WorkProductPlanTemplateCopyModal @register="registerCopyModal" @success="handleSuccess" />
    <WorkProductPlanTemplateDetailModal @register="registerDetailModal" />
  </div>
</template>

<script lang="ts" setup>
  import { reactive, ref } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { useModal } from '/@/components/Modal';
  import { Icon } from '/@/components/Icon';
  import { useMessage } from '/@/hooks/web/useMessage';
  
  import WorkProductPlanTemplateModal from './WorkProductPlanTemplateModal.vue';
  import WorkProductPlanTemplateCopyModal from './WorkProductPlanTemplateCopyModal.vue';
  import WorkProductPlanTemplateDetailModal from './WorkProductPlanTemplateDetailModal.vue';
  import { columns, searchFormSchema } from './workProductPlanTemplate.data';
  import {
    getWorkProductPlanTemplateList,
    deleteWorkProductPlanTemplate,
    batchDeleteWorkProductPlanTemplate,
    publishWorkProductPlanTemplate,
    archiveWorkProductPlanTemplate,
    batchUpdateWorkProductPlanTemplateKnStatus,
  } from '/@/api/project/workProductPlanTemplate';

  defineOptions({ name: 'project.template.workProductPlanTemplate' });

  const { createMessage } = useMessage();
  const [registerModal, { openModal }] = useModal();
  const [registerCopyModal, { openModal: openCopyModal }] = useModal();
  const [registerDetailModal, { openModal: openDetailModal }] = useModal();
  const searchInfo = reactive<Recordable>({});
  const selectedRowKeys = ref<string[]>([]);

  const [registerTable, { reload, getSelectRowKeys, updateTableDataRecord }] = useTable({
    title: '交付物计划模板列表',
    api: getWorkProductPlanTemplateList,
    rowKey: 'id',
    columns,
    formConfig: {
      labelWidth: 120,
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
    },
    useSearchForm: true,
    showTableSetting: true,
    bordered: true,
    handleSearchInfoFn(info) {
      console.log('handleSearchInfoFn', info);
      return info;
    },
    actionColumn: {
      width: 200,
      title: '操作',
      dataIndex: 'action',
    },
    rowSelection: {
      type: 'checkbox',
      onChange: (keys: string[]) => {
        selectedRowKeys.value = keys;
      },
    },
  });

  function handleCreate() {
    openModal(true, {
      isUpdate: false,
    });
  }

  function handleEdit(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
    });
  }

  function handleView(record: Recordable) {
    openDetailModal(true, {
      record,
    });
  }

  function handleCopy(record: Recordable) {
    openCopyModal(true, {
      record,
    });
  }

  async function handlePublish(record: Recordable) {
    try {
      await publishWorkProductPlanTemplate(record.id);
      createMessage.success('发布成功');
      reload();
    } catch (error) {
      console.error('发布失败:', error);
    }
  }

  async function handleArchive(record: Recordable) {
    try {
      await archiveWorkProductPlanTemplate(record.id);
      createMessage.success('归档成功');
      reload();
    } catch (error) {
      console.error('归档失败:', error);
    }
  }

  async function handleDelete(record: Recordable) {
    try {
      await deleteWorkProductPlanTemplate(record.id);
      createMessage.success('删除成功');
      reload();
    } catch (error) {
      console.error('删除失败:', error);
    }
  }

  async function handleBatchDelete() {
    try {
      const keys = getSelectRowKeys();
      if (keys.length === 0) {
        createMessage.warning('请选择要删除的数据');
        return;
      }
      await batchDeleteWorkProductPlanTemplate(keys);
      createMessage.success('批量删除成功');
      selectedRowKeys.value = [];
      reload();
    } catch (error) {
      console.error('批量删除失败:', error);
    }
  }

  async function handleBatchPublish() {
    try {
      const keys = getSelectRowKeys();
      if (keys.length === 0) {
        createMessage.warning('请选择要发布的数据');
        return;
      }
      await batchUpdateWorkProductPlanTemplateKnStatus(keys, 'published');
      createMessage.success('批量发布成功');
      selectedRowKeys.value = [];
      reload();
    } catch (error) {
      console.error('批量发布失败:', error);
    }
  }

  function handleSuccess({ isUpdate, values }) {
    if (isUpdate) {
      const result = updateTableDataRecord(values.id, values);
      console.log(result);
    } else {
      reload();
    }
  }
</script>
