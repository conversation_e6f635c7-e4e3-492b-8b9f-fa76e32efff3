<template>
  <div class="batch-actions" v-show="selectedCount > 0">
    <div class="batch-info">
      <a-checkbox
        :indeterminate="indeterminate"
        :checked="checkAll"
        @change="handleSelectAllChange"
      >
        已选择 {{ selectedCount }} 项
      </a-checkbox>
      <a-button type="link" size="small" @click="clearSelection">
        清空选择
      </a-button>
    </div>

    <div class="batch-operations">
      <a-space>
        <!-- 批量删除 -->
        <a-button
          v-if="actions.includes('delete')"
          danger
          :loading="loading.delete"
          @click="handleBatchDelete"
        >
          <Icon icon="ant-design:delete-outlined" />
          批量删除
        </a-button>

        <!-- 批量启用 -->
        <a-button
          v-if="actions.includes('enable')"
          type="primary"
          ghost
          :loading="loading.enable"
          @click="handleBatchEnable"
        >
          <Icon icon="ant-design:check-outlined" />
          批量启用
        </a-button>

        <!-- 批量禁用 -->
        <a-button
          v-if="actions.includes('disable')"
          :loading="loading.disable"
          @click="handleBatchDisable"
        >
          <Icon icon="ant-design:stop-outlined" />
          批量禁用
        </a-button>

        <!-- 批量发布 -->
        <a-button
          v-if="actions.includes('publish')"
          type="primary"
          ghost
          :loading="loading.publish"
          @click="handleBatchPublish"
        >
          <Icon icon="ant-design:cloud-upload-outlined" />
          批量发布
        </a-button>

        <!-- 批量归档 -->
        <a-button
          v-if="actions.includes('archive')"
          :loading="loading.archive"
          @click="handleBatchArchive"
        >
          <Icon icon="ant-design:inbox-outlined" />
          批量归档
        </a-button>

        <!-- 批量移动 -->
        <a-button
          v-if="actions.includes('move')"
          :loading="loading.move"
          @click="handleBatchMove"
        >
          <Icon icon="ant-design:drag-outlined" />
          批量移动
        </a-button>

        <!-- 批量复制 -->
        <a-button
          v-if="actions.includes('copy')"
          :loading="loading.copy"
          @click="handleBatchCopy"
        >
          <Icon icon="ant-design:copy-outlined" />
          批量复制
        </a-button>

        <!-- 批量导出 -->
        <DataExport
          v-if="actions.includes('export')"
          :data="selectedData"
          :columns="columns"
          :selected-data="selectedData"
          default-filename="batch_export"
          @export="handleBatchExport"
        />

        <!-- 自定义操作 -->
        <a-dropdown v-if="customActions.length > 0" :trigger="['click']">
          <a-button>
            更多操作
            <Icon icon="ant-design:down-outlined" />
          </a-button>
          <template #overlay>
            <a-menu @click="handleCustomAction">
              <a-menu-item
                v-for="action in customActions"
                :key="action.key"
                :disabled="action.disabled"
              >
                <Icon v-if="action.icon" :icon="action.icon" />
                {{ action.label }}
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </a-space>
    </div>

    <!-- 批量操作进度 -->
    <div v-if="showProgress" class="batch-progress">
      <a-progress
        :percent="progressPercent"
        :status="progressStatus"
        size="small"
      />
      <span class="progress-text">{{ progressText }}</span>
    </div>
  </div>

  <!-- 批量移动弹窗 -->
  <BasicModal
    @register="registerMoveModal"
    title="批量移动"
    width="500px"
    @ok="handleConfirmMove"
  >
    <div class="move-config">
      <p>将 {{ selectedCount }} 个项目移动到：</p>
      <a-tree-select
        v-model:value="moveTarget"
        :tree-data="moveTargetOptions"
        placeholder="请选择目标位置"
        tree-default-expand-all
        style="width: 100%"
      />
    </div>
  </BasicModal>

  <!-- 批量复制弹窗 -->
  <BasicModal
    @register="registerCopyModal"
    title="批量复制"
    width="500px"
    @ok="handleConfirmCopy"
  >
    <div class="copy-config">
      <p>复制 {{ selectedCount }} 个项目：</p>
      <a-form layout="vertical">
        <a-form-item label="命名规则">
          <a-radio-group v-model:value="copyNamingRule">
            <a-radio value="suffix">添加后缀</a-radio>
            <a-radio value="prefix">添加前缀</a-radio>
            <a-radio value="custom">自定义</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item v-if="copyNamingRule !== 'custom'" label="文本">
          <a-input v-model:value="copyNamingText" placeholder="请输入文本" />
        </a-form-item>
        <a-form-item label="目标位置">
          <a-tree-select
            v-model:value="copyTarget"
            :tree-data="moveTargetOptions"
            placeholder="请选择目标位置"
            tree-default-expand-all
            style="width: 100%"
          />
        </a-form-item>
      </a-form>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, computed, reactive } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { Icon } from '/@/components/Icon';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useTemplateActions } from '../hooks/useTemplateActions';
  import DataExport from './DataExport.vue';

  interface CustomAction {
    key: string;
    label: string;
    icon?: string;
    disabled?: boolean;
  }

  interface Props {
    selectedData: any[];
    totalCount: number;
    actions: string[];
    customActions?: CustomAction[];
    columns?: any[];
    moveTargetOptions?: any[];
  }

  const props = withDefaults(defineProps<Props>(), {
    customActions: () => [],
    columns: () => [],
    moveTargetOptions: () => [],
  });

  const emit = defineEmits<{
    selectAll: [checked: boolean];
    clearSelection: [];
    batchDelete: [ids: string[]];
    batchEnable: [ids: string[]];
    batchDisable: [ids: string[]];
    batchPublish: [ids: string[]];
    batchArchive: [ids: string[]];
    batchMove: [ids: string[], target: string];
    batchCopy: [ids: string[], config: any];
    customAction: [action: string, ids: string[]];
  }>();

  const { createMessage } = useMessage();
  const { handleBatchAction, handleBatchDelete: handleBatchDeleteAction } = useTemplateActions();

  // 状态
  const loading = reactive({
    delete: false,
    enable: false,
    disable: false,
    publish: false,
    archive: false,
    move: false,
    copy: false,
  });

  const showProgress = ref(false);
  const progressPercent = ref(0);
  const progressStatus = ref<'normal' | 'exception' | 'success'>('normal');
  const progressText = ref('');

  // 移动相关
  const moveTarget = ref('');
  const [registerMoveModal, { openModal: openMoveModal, closeModal: closeMoveModal }] = useModalInner();

  // 复制相关
  const copyTarget = ref('');
  const copyNamingRule = ref('suffix');
  const copyNamingText = ref('_副本');
  const [registerCopyModal, { openModal: openCopyModal, closeModal: closeCopyModal }] = useModalInner();

  // 计算属性
  const selectedCount = computed(() => props.selectedData.length);
  const selectedIds = computed(() => props.selectedData.map(item => item.id));

  const checkAll = computed(() => {
    return selectedCount.value === props.totalCount && props.totalCount > 0;
  });

  const indeterminate = computed(() => {
    return selectedCount.value > 0 && selectedCount.value < props.totalCount;
  });

  // 方法
  function handleSelectAllChange(e: any) {
    emit('selectAll', e.target.checked);
  }

  function clearSelection() {
    emit('clearSelection');
  }

  async function handleBatchDelete() {
    const result = await handleBatchDeleteAction(
      () => Promise.resolve(),
      selectedCount.value
    );
    
    if (result) {
      emit('batchDelete', selectedIds.value);
    }
  }

  async function handleBatchEnable() {
    await executeBatchAction('enable', '启用');
  }

  async function handleBatchDisable() {
    await executeBatchAction('disable', '禁用');
  }

  async function handleBatchPublish() {
    await executeBatchAction('publish', '发布');
  }

  async function handleBatchArchive() {
    await executeBatchAction('archive', '归档');
  }

  function handleBatchMove() {
    moveTarget.value = '';
    openMoveModal();
  }

  function handleBatchCopy() {
    copyTarget.value = '';
    copyNamingRule.value = 'suffix';
    copyNamingText.value = '_副本';
    openCopyModal();
  }

  async function handleConfirmMove() {
    if (!moveTarget.value) {
      createMessage.warning('请选择目标位置');
      return;
    }

    loading.move = true;
    try {
      emit('batchMove', selectedIds.value, moveTarget.value);
      createMessage.success('批量移动成功');
      closeMoveModal();
    } catch (error) {
      createMessage.error('批量移动失败');
    } finally {
      loading.move = false;
    }
  }

  async function handleConfirmCopy() {
    if (!copyTarget.value) {
      createMessage.warning('请选择目标位置');
      return;
    }

    if (copyNamingRule.value !== 'custom' && !copyNamingText.value) {
      createMessage.warning('请输入命名文本');
      return;
    }

    loading.copy = true;
    try {
      const config = {
        target: copyTarget.value,
        namingRule: copyNamingRule.value,
        namingText: copyNamingText.value,
      };
      emit('batchCopy', selectedIds.value, config);
      createMessage.success('批量复制成功');
      closeCopyModal();
    } catch (error) {
      createMessage.error('批量复制失败');
    } finally {
      loading.copy = false;
    }
  }

  function handleCustomAction({ key }: { key: string }) {
    emit('customAction', key, selectedIds.value);
  }

  function handleBatchExport(config: any, data: any[]) {
    createMessage.success(`成功导出 ${data.length} 条数据`);
  }

  async function executeBatchAction(action: string, actionName: string) {
    const loadingKey = action as keyof typeof loading;
    
    const result = await handleBatchAction(
      () => Promise.resolve(),
      actionName,
      selectedCount.value,
      true
    );

    if (result) {
      const emitMap = {
        enable: 'batchEnable',
        disable: 'batchDisable',
        publish: 'batchPublish',
        archive: 'batchArchive',
      };
      
      const eventName = emitMap[action as keyof typeof emitMap];
      if (eventName) {
        emit(eventName as any, selectedIds.value);
      }
    }
  }

  // 模拟进度更新
  function simulateProgress(action: string) {
    showProgress.value = true;
    progressPercent.value = 0;
    progressStatus.value = 'normal';
    progressText.value = `正在${action}...`;

    const interval = setInterval(() => {
      progressPercent.value += 10;
      if (progressPercent.value >= 100) {
        clearInterval(interval);
        progressStatus.value = 'success';
        progressText.value = `${action}完成`;
        setTimeout(() => {
          showProgress.value = false;
        }, 2000);
      }
    }, 200);
  }
</script>

<style lang="less" scoped>
  .batch-actions {
    background: #e6f7ff;
    border: 1px solid #91d5ff;
    border-radius: 6px;
    padding: 12px 16px;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 12px;

    .batch-info {
      display: flex;
      align-items: center;
      gap: 12px;
      color: #1890ff;
      font-weight: 500;
    }

    .batch-operations {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .batch-progress {
      width: 100%;
      display: flex;
      align-items: center;
      gap: 12px;
      margin-top: 8px;

      .progress-text {
        font-size: 12px;
        color: #666;
      }
    }
  }

  .move-config,
  .copy-config {
    p {
      margin-bottom: 16px;
      color: #666;
    }
  }
</style>
