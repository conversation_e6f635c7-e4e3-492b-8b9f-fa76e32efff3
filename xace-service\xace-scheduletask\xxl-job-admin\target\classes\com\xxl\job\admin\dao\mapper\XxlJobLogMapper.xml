<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xxl.job.admin.dao.XxlJobLogMapper">

    <resultMap id="XxlJobLog" type="XxlJobLog">
        <result column="id" property="id"/>

        <result column="job_group" property="jobGroup"/>
        <result column="job_id" property="jobId"/>

        <result column="executor_address" property="executorAddress"/>
        <result column="executor_handler" property="executorHandler"/>
        <result column="executor_param" property="executorParam"/>
        <result column="executor_sharding_param" property="executorShardingParam"/>
        <result column="executor_fail_retry_count" property="executorFailRetryCount"/>

        <result column="trigger_time" property="triggerTime"/>
        <result column="trigger_code" property="triggerCode"/>
        <result column="trigger_msg" property="triggerMsg"/>

        <result column="handle_time" property="handleTime"/>
        <result column="handle_code" property="handleCode"/>
        <result column="handle_msg" property="handleMsg"/>

        <result column="alarm_status" property="alarmStatus"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id,
		t.job_group,
		t.job_id,
		t.executor_address,
		t.executor_handler,
		t.executor_param,
		t.executor_sharding_param,
		t.executor_fail_retry_count,
		t.trigger_time,
		t.trigger_code,
		t.trigger_msg,
		t.handle_time,
		t.handle_code,
		t.handle_msg,
		t.alarm_status
    </sql>

    <!--<select id="triggerCountByDay" resultType="java.util.Map" >
        SELECT
            DATE_FORMAT(trigger_time,'%Y-%m-%d') triggerDay,
            COUNT(handle_code) triggerDayCount,
            SUM(CASE WHEN (trigger_code in (0, 200) and handle_code = 0) then 1 else 0 end) as triggerDayCountRunning,
            SUM(CASE WHEN handle_code = 200 then 1 else 0 end) as triggerDayCountSuc
        FROM xxl_job_log
        WHERE trigger_time BETWEEN #{from} and #{to}
        GROUP BY triggerDay
        ORDER BY triggerDay
    </select>-->

    <select id="findLogReport" resultType="java.util.Map">
        SELECT
        COUNT(handle_code) triggerDayCount,
        SUM(CASE WHEN (trigger_code in (0, 200) and handle_code = 0) then 1 else 0 end) as triggerDayCountRunning,
        SUM(CASE WHEN handle_code = 200 then 1 else 0 end) as triggerDayCountSuc
        FROM xxl_job_log
        <if test="dbType != null and dbType != '' and dbType == 'MySQL'">
            WHERE trigger_time BETWEEN #{from} and #{to}
        </if>
        <if test="dbType != null and dbType != '' and dbType == 'SQLServer'">
            WHERE trigger_time BETWEEN #{from} and #{to}
        </if>
        <if test="dbType != null and dbType != '' and dbType == 'Oracle'">
            WHERE trigger_time BETWEEN TO_DATE(#{from}, 'yyyy-mm-dd hh24:mi:ss') and TO_DATE(#{to}, 'yyyy-mm-dd
            hh24:mi:ss')
        </if>
        <if test="dbType != null and dbType != '' and dbType == 'DM8'">
            WHERE trigger_time BETWEEN #{from} and #{to}
        </if>
        <if test="dbType != null and dbType != '' and dbType == 'KingbaseES'">
            WHERE trigger_time BETWEEN #{from} and #{to}
        </if>
        <if test="dbType != null and dbType != '' and dbType == 'PostgreSQL'">
            WHERE trigger_time BETWEEN #{from} and #{to}
        </if>
    </select>

    <select id="oracleFindLogReport" resultType="java.util.Map">
        SELECT
            COUNT(handle_code) triggerDayCount,
            SUM(CASE WHEN (trigger_code in (0, 200) and handle_code = 0) then 1 else 0 end) as triggerDayCountRunning,
            SUM(CASE WHEN handle_code = 200 then 1 else 0 end) as triggerDayCountSuc
        FROM xxl_job_log
        WHERE trigger_time BETWEEN TO_DATE(#{from}, 'yyyy-mm-dd hh24:mi:ss') and TO_DATE(#{to}, 'yyyy-mm-dd hh24:mi:ss')
    </select>

    <select id="findLostJobIds" resultType="String">
        SELECT
        t.id
        FROM
        xxl_job_log t
        LEFT JOIN xxl_job_registry t2 ON t.executor_address = t2.registry_value
        WHERE
        t.trigger_code = 200
        AND t.handle_code = 0
        AND t2.id IS NULL
        <if test="dbType != null and dbType != '' and dbType == 'MySQL'">
            AND t.trigger_time &gt;= #{losedTime}
        </if>
        <if test="dbType != null and dbType != '' and dbType == 'SQLServer'">
            AND t.trigger_time &gt;= #{losedTime}
        </if>
        <if test="dbType != null and dbType != '' and dbType == 'Oracle'">
            AND t.trigger_time &gt;= TO_DATE(#{losedTime}, 'yyyy-mm-dd hh24:mi:ss')
        </if>
        <if test="dbType != null and dbType != '' and dbType == 'DM8'">
            AND t.trigger_time &gt;= #{losedTime}
        </if>
        <if test="dbType != null and dbType != '' and dbType == 'KingbaseES'">
            AND t.trigger_time &gt;= #{losedTime}
        </if>
        <if test="dbType != null and dbType != '' and dbType == 'PostgreSQL'">
            AND t.trigger_time &gt;= #{losedTime}
        </if>

    </select>
    <!--
    SELECT t.id
    FROM xxl_job_log AS t
    WHERE t.trigger_code = 200
        and t.handle_code = 0
        and t.trigger_time <![CDATA[ <= ]]> #{losedTime}
        and t.executor_address not in (
            SELECT t2.registry_value
            FROM xxl_job_registry AS t2
        )
    -->

</mapper>
