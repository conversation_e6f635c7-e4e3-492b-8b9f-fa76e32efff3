<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xxl.job.admin.dao.XxlJobRegistryMapper">

    <resultMap id="XxlJobRegistry" type="com.xxl.job.admin.core.model.XxlJobRegistry">
        <result column="id" property="id"/>
        <result column="registry_group" property="registryGroup"/>
        <result column="registry_key" property="registryKey"/>
        <result column="registry_value" property="registryValue"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
		t.id,
		t.registry_group,
		t.registry_key,
		t.registry_value,
		t.update_time
	</sql>

</mapper>
