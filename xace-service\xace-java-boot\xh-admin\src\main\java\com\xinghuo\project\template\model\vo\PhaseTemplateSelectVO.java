package com.xinghuo.project.template.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 阶段模板选择列表视图对象
 * 用于下拉选择框等场景，只包含必要的id和fullName字段
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
@Data
@Schema(description = "阶段模板选择列表视图对象")
public class PhaseTemplateSelectVO {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private String id;

    /**
     * 完整名称 (格式: [编码] 名称)
     * 用于option显示，符合fullName和id的规范要求
     */
    @Schema(description = "完整名称")
    private String fullName;

    /**
     * 阶段编码 (用于构建fullName)
     */
    @Schema(description = "阶段编码")
    private String code;

    /**
     * 阶段名称 (用于构建fullName)
     */
    @Schema(description = "阶段名称")
    private String name;

    /**
     * 状态 (1:启用, 0:禁用)
     * 用于过滤，通常只显示启用状态的选项
     */
    @Schema(description = "状态")
    private Integer status;
}
