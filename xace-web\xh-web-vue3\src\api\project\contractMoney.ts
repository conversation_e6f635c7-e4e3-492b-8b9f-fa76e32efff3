import { defHttp } from '/@/utils/http/axios';
import { ListResult } from '/@/api/model/baseModel';

/**
 * 合同收款管理API
 */

// API URL前缀
const API_PREFIX = '/api/project/biz/contract/money';

/**
 * 合同收款对象接口
 */
export interface ContractMoneyModel {
  cmId: string;
  cid: string;
  contractName?: string;
  contractNo?: string;
  fktj: string;
  ratio: number;
  cmMoney: number;
  yushouDate: string;
  yingshouDate: string;
  kaipiaoDate: string;
  shoukuanDate: string;
  payStatus: string;
  note: string;
  lastNote: string;
  ownId: string;
  ownName?: string;
  deptId: string;
  deptName?: string;
  ybAmount: number;
  ebAmount: number;
  otherAmount: number;
  createUserId: string;
  createTime: string;
  lastModifiedUserId: string;
  updateTime: string;
}

/**
 * 合同收款表单接口
 */
export interface ContractMoneyFormModel {
  cid: string;
  fktj: string;
  ratio?: number;
  cmMoney: number;
  yushouDate?: string;
  yingshouDate?: string;
  kaipiaoDate?: string;
  shoukuanDate?: string;
  payStatus?: string;
  note?: string;
  ownId?: string;
  deptId?: string;
  ybAmount?: number;
  ebAmount?: number;
  otherAmount?: number;
}

/**
 * 合同收款查询参数接口
 */
export interface ContractMoneyQueryParams {
  cid?: string;
  contractName?: string;
  contractNo?: string;
  payStatus?: string;
  yingshouDateStart?: string;
  yingshouDateEnd?: string;
  shoukuanDateStart?: string;
  shoukuanDateEnd?: string;
  ownId?: string;
  deptId?: string;
  keyword?: string;
  dateType?: string;
  timeRange?: string[];
  pageSize?: number;
  currentPage?: number;
}

/**
 * 合同收款状态更新接口
 */
export interface ContractMoneyStatusModel {
  payStatus: string;
  kaipiaoDate?: string;
  shoukuanDate?: string;
  lastNote?: string;
}

/**
 * 获取合同收款列表
 * @param params 查询参数
 * @returns 合同收款列表
 */
export const getContractMoneyList = (params?: ContractMoneyQueryParams) => {
  return defHttp.post<ListResult<ContractMoneyModel>>({
    url: `${API_PREFIX}/getList`,
    data: params,
  });
};

/**
 * 根据合同ID获取收款列表
 * @param contractId 合同ID
 * @returns 收款列表
 */
export const getContractMoneyListByContractId = (contractId: string) => {
  return defHttp.get<ContractMoneyModel[]>({
    url: `${API_PREFIX}/contract/${contractId}`,
  });
};

/**
 * 获取合同收款详情
 * @param id 合同收款ID
 * @returns 合同收款详情
 */
export const getContractMoneyInfo = (id: string) => {
  return defHttp.get<ContractMoneyModel>({
    url: `${API_PREFIX}/${id}`,
  });
};

/**
 * 创建合同收款
 * @param params 合同收款创建参数
 * @returns 操作结果
 */
export const createContractMoney = (params: ContractMoneyFormModel) => {
  return defHttp.post<void>({
    url: API_PREFIX,
    data: params,
  });
};

/**
 * 更新合同收款
 * @param id 合同收款ID
 * @param params 合同收款更新参数
 * @returns 操作结果
 */
export const updateContractMoney = (id: string, params: ContractMoneyFormModel) => {
  return defHttp.put<void>({
    url: `${API_PREFIX}/${id}`,
    data: params,
  });
};

/**
 * 删除合同收款
 * @param id 合同收款ID
 * @returns 操作结果
 */
export const deleteContractMoney = (id: string) => {
  return defHttp.delete<void>({
    url: `${API_PREFIX}/${id}`,
  });
};

/**
 * 更新收款状态
 * @param id 合同收款ID
 * @param params 状态更新参数
 * @returns 操作结果
 */
export const updateContractMoneyStatus = (id: string, params: ContractMoneyStatusModel) => {
  return defHttp.put<void>({
    url: `${API_PREFIX}/${id}/status`,
    data: params,
  });
};

/**
 * 登记开票
 * @param id 合同收款ID
 * @param kaipiaoDate 开票日期
 * @param lastNote 备注
 * @returns 操作结果
 */
export const registerContractMoneyInvoice = (id: string, kaipiaoDate: string, lastNote?: string) => {
  return defHttp.put<void>({
    url: `${API_PREFIX}/${id}/invoice`,
    params: {
      kaipiaoDate,
      lastNote,
    },
  });
};

/**
 * 登记收款
 * @param id 合同收款ID
 * @param shoukuanDate 收款日期
 * @param lastNote 备注
 * @returns 操作结果
 */
export const registerContractMoneyPayment = (id: string, shoukuanDate: string, lastNote?: string) => {
  return defHttp.put<void>({
    url: `${API_PREFIX}/${id}/payment`,
    params: {
      shoukuanDate,
      lastNote,
    },
  });
};

/**
 * 收款统计数据接口
 */
export interface ContractMoneyStatistics {
  // 待收款总金额
  pendingAmount: number;
  // 本年度已收款金额
  yearPaidAmount: number;
  // 本年度待收款金额
  yearPendingAmount: number;
  // 最近一个月待收款金额
  oneMonthPendingAmount: number;
  // 最近三个月待收款金额
  threeMonthPendingAmount: number;
  // 已开票未收款金额
  invoicedUnpaidAmount: number;
  // 今年开票金额
  yearInvoicedAmount: number;
  // 今年已开票未收款金额
  yearInvoicedUnpaidAmount: number;
  // 总收款记录数
  totalCount: number;
  // 已收款记录数
  paidCount: number;
  // 待收款记录数
  pendingCount: number;
  // 已开票记录数
  invoicedCount: number;
}

/**
 * 获取收款统计数据
 * @returns 收款统计数据
 */
export const getContractMoneyStatistics = () => {
  return defHttp.get<ContractMoneyStatistics>({
    url: `${API_PREFIX}/statistics`,
  });
};
