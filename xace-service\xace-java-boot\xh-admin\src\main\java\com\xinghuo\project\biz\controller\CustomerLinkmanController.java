package com.xinghuo.project.biz.controller;

import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.project.biz.entity.CustomerContactEntity;
import com.xinghuo.project.biz.model.CustomerLinkmanPagination;
import com.xinghuo.project.biz.service.CustomerContactService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 客户联系人管理控制器
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Slf4j
@Tag(name = "客户联系人管理", description = "客户联系人管理相关接口")
@RestController
@RequestMapping("/api/project/biz/customer/linkman")
public class CustomerLinkmanController {

    @Resource
    private CustomerContactService customerContactService;

    /**
     * 获取客户联系人列表
     */
    @PostMapping("/getList")
    @Operation(summary = "获取客户联系人列表")
    public ActionResult<PageListVO<CustomerContactEntity>> list(@RequestBody CustomerLinkmanPagination pagination) {
        List<CustomerContactEntity> list = customerContactService.getList(pagination);
        PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
        return ActionResult.page(list, page);
    }

    /**
     * 根据客户ID获取联系人列表
     */
    @GetMapping("/getListByCustomerId/{customerId}")
    @Operation(summary = "根据客户ID获取联系人列表")
    public ActionResult<List<CustomerContactEntity>> getListByCustomerId(
            @Parameter(description = "客户ID") @PathVariable String customerId) {
        List<CustomerContactEntity> list = customerContactService.getListByCustomerId(customerId);
        return ActionResult.success(list);
    }

    /**
     * 获取客户联系人详情
     */
    @GetMapping("/getInfo/{id}")
    @Operation(summary = "获取客户联系人详情")
    public ActionResult<CustomerContactEntity> getInfo(
            @Parameter(description = "联系人ID") @PathVariable String id) {
        CustomerContactEntity entity = customerContactService.getInfo(id);
        return ActionResult.success(entity);
    }

    /**
     * 创建客户联系人
     */
    @PostMapping("/create")
    @Operation(summary = "创建客户联系人")
    public ActionResult<String> create(@RequestBody @Valid CustomerContactEntity entity) {
        String id = customerContactService.create(entity);
        return ActionResult.success("创建成功", id);
    }

    /**
     * 更新客户联系人
     */
    @PutMapping("/update/{id}")
    @Operation(summary = "更新客户联系人")
    public ActionResult<String> update(
            @Parameter(description = "联系人ID") @PathVariable String id,
            @RequestBody @Valid CustomerContactEntity entity) {
        customerContactService.update(id, entity);
        return ActionResult.success("更新成功");
    }

    /**
     * 删除客户联系人
     */
    @DeleteMapping("/delete/{id}")
    @Operation(summary = "删除客户联系人")
    public ActionResult<String> delete(
            @Parameter(description = "联系人ID") @PathVariable String id) {
        customerContactService.delete(id);
        return ActionResult.success("删除成功");
    }

    /**
     * 更新客户联系人状态
     */
    @PutMapping("/updateStatus/{id}")
    @Operation(summary = "更新客户联系人状态")
    public ActionResult<String> updateStatus(
            @Parameter(description = "联系人ID") @PathVariable String id,
            @RequestParam Integer status) {
        customerContactService.updateStatus(id, status);
        return ActionResult.success("状态更新成功");
    }

    /**
     * 获取客户联系人选择列表
     */
    @GetMapping("/getSelectList")
    @Operation(summary = "获取客户联系人选择列表")
    public ActionResult<List<CustomerContactEntity>> getSelectList(
            @RequestParam(required = false) String customerId,
            @RequestParam(required = false) String keyword) {
        List<CustomerContactEntity> list = customerContactService.getSelectList(customerId, keyword);
        return ActionResult.success(list);
    }
}
