import { ref, reactive } from 'vue';
import { useMessage } from '/@/hooks/web/useMessage';

/**
 * 模板操作通用Hook
 * 提供统一的错误处理、加载状态管理、操作反馈等功能
 */
export function useTemplateActions() {
  const { createMessage, createConfirm } = useMessage();

  // 加载状态管理
  const loading = reactive({
    list: false,
    create: false,
    update: false,
    delete: false,
    batch: false,
    detail: false,
    export: false,
    import: false,
  });

  // 错误状态
  const error = ref<string | null>(null);

  /**
   * 执行异步操作的通用方法
   */
  async function executeAction<T>(
    action: () => Promise<T>,
    loadingKey: keyof typeof loading,
    successMessage?: string,
    errorMessage?: string
  ): Promise<T | null> {
    try {
      loading[loadingKey] = true;
      error.value = null;
      
      const result = await action();
      
      if (successMessage) {
        createMessage.success(successMessage);
      }
      
      return result;
    } catch (err: any) {
      const message = errorMessage || err.message || '操作失败';
      error.value = message;
      createMessage.error(message);
      console.error('操作失败:', err);
      return null;
    } finally {
      loading[loadingKey] = false;
    }
  }

  /**
   * 创建操作
   */
  async function handleCreate<T>(
    createFn: () => Promise<T>,
    successMessage = '创建成功'
  ): Promise<T | null> {
    return executeAction(createFn, 'create', successMessage, '创建失败');
  }

  /**
   * 更新操作
   */
  async function handleUpdate<T>(
    updateFn: () => Promise<T>,
    successMessage = '更新成功'
  ): Promise<T | null> {
    return executeAction(updateFn, 'update', successMessage, '更新失败');
  }

  /**
   * 删除操作（带确认）
   */
  async function handleDelete<T>(
    deleteFn: () => Promise<T>,
    itemName: string,
    successMessage = '删除成功'
  ): Promise<T | null> {
    return new Promise((resolve) => {
      createConfirm({
        iconType: 'warning',
        title: '删除确认',
        content: `确定要删除"${itemName}"吗？删除后无法恢复。`,
        onOk: async () => {
          const result = await executeAction(deleteFn, 'delete', successMessage, '删除失败');
          resolve(result);
        },
        onCancel: () => {
          resolve(null);
        },
      });
    });
  }

  /**
   * 批量删除操作
   */
  async function handleBatchDelete<T>(
    batchDeleteFn: () => Promise<T>,
    count: number,
    successMessage = '批量删除成功'
  ): Promise<T | null> {
    if (count === 0) {
      createMessage.warning('请选择要删除的记录');
      return null;
    }

    return new Promise((resolve) => {
      createConfirm({
        iconType: 'warning',
        title: '批量删除确认',
        content: `确定要删除选中的 ${count} 条记录吗？删除后无法恢复。`,
        onOk: async () => {
          const result = await executeAction(batchDeleteFn, 'batch', successMessage, '批量删除失败');
          resolve(result);
        },
        onCancel: () => {
          resolve(null);
        },
      });
    });
  }

  /**
   * 批量操作
   */
  async function handleBatchAction<T>(
    batchActionFn: () => Promise<T>,
    actionName: string,
    count: number,
    needConfirm = false
  ): Promise<T | null> {
    if (count === 0) {
      createMessage.warning(`请选择要${actionName}的记录`);
      return null;
    }

    if (needConfirm) {
      return new Promise((resolve) => {
        createConfirm({
          iconType: 'info',
          title: `批量${actionName}确认`,
          content: `确定要${actionName}选中的 ${count} 条记录吗？`,
          onOk: async () => {
            const result = await executeAction(
              batchActionFn, 
              'batch', 
              `批量${actionName}成功`, 
              `批量${actionName}失败`
            );
            resolve(result);
          },
          onCancel: () => {
            resolve(null);
          },
        });
      });
    } else {
      return executeAction(
        batchActionFn, 
        'batch', 
        `批量${actionName}成功`, 
        `批量${actionName}失败`
      );
    }
  }

  /**
   * 状态切换操作
   */
  async function handleStatusToggle<T>(
    toggleFn: () => Promise<T>,
    currentStatus: boolean | number,
    itemName: string
  ): Promise<T | null> {
    const action = (currentStatus === 1 || currentStatus === true) ? '禁用' : '启用';
    return executeAction(toggleFn, 'update', `${action}成功`, `${action}失败`);
  }

  /**
   * 复制操作
   */
  async function handleCopy<T>(
    copyFn: () => Promise<T>,
    successMessage = '复制成功'
  ): Promise<T | null> {
    return executeAction(copyFn, 'create', successMessage, '复制失败');
  }

  /**
   * 导出操作
   */
  async function handleExport<T>(
    exportFn: () => Promise<T>,
    successMessage = '导出成功'
  ): Promise<T | null> {
    return executeAction(exportFn, 'export', successMessage, '导出失败');
  }

  /**
   * 导入操作
   */
  async function handleImport<T>(
    importFn: () => Promise<T>,
    successMessage = '导入成功'
  ): Promise<T | null> {
    return executeAction(importFn, 'import', successMessage, '导入失败');
  }

  /**
   * 获取详情操作
   */
  async function handleGetDetail<T>(
    getDetailFn: () => Promise<T>
  ): Promise<T | null> {
    return executeAction(getDetailFn, 'detail', undefined, '获取详情失败');
  }

  /**
   * 清除错误状态
   */
  function clearError() {
    error.value = null;
  }

  /**
   * 检查是否有任何加载状态
   */
  function isAnyLoading(): boolean {
    return Object.values(loading).some(Boolean);
  }

  /**
   * 重置所有状态
   */
  function resetStates() {
    Object.keys(loading).forEach(key => {
      loading[key as keyof typeof loading] = false;
    });
    error.value = null;
  }

  return {
    loading,
    error,
    executeAction,
    handleCreate,
    handleUpdate,
    handleDelete,
    handleBatchDelete,
    handleBatchAction,
    handleStatusToggle,
    handleCopy,
    handleExport,
    handleImport,
    handleGetDetail,
    clearError,
    isAnyLoading,
    resetStates,
  };
}
