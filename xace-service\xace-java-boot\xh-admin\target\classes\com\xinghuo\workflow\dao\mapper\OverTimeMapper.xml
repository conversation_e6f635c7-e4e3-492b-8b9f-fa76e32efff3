<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinghuo.workflow.dao.OverTimeMapper">
    <select id="overTimeVocList" parameterType="String"
            resultType="com.xinghuo.workflow.model.overtime.OverTimeSimpleVO">
        select id,(hours-leave_hours) remain_hours, DATE_FORMAT(start_time,'%Y-%m-%d')  over_date from zz_kq_overtime_leave w
       where w.is_expired=0 and w.hours>w.leave_hours and w.user_id=#{userId} order by w.start_time
    </select>

</mapper>
