<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinghuo.manhour.dao.ProjEventMapper">

   <select id="selectSameData" resultType="com.xinghuo.manhour.entity.ProjEventEntity">
       select distinct e1.f_id as id
        FROM zz_proj_event e1
        JOIN zz_proj_event e2 ON
            e1.pm_no = e2.pm_no
            AND e1.project_id=e2.project_id
                AND e1.module_name=e2.module_name
            AND e1.event_point = e2.event_point
            AND e1.create_user_name = e2.create_user_name
            AND e1.create_date = e2.create_date
            AND e1.event_descri = e2.event_descri
            AND e1.event_type = e2.event_type
            AND e1.deal_user_name = e2.deal_user_name
            AND e1.start_deal_time = e2.start_deal_time
            AND e1.handle_time = e2.handle_time
            AND e1.deal_state = e2.deal_state
            AND e1.need_optimize = e2.need_optimize
        WHERE e1.f_id > e2.f_id
   </select>

</mapper>
