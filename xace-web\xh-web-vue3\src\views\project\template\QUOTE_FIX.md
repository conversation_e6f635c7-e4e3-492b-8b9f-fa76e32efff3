# Vue模板引号错误修复说明

## 问题描述

在Vue模板中出现了编译错误：
```
[plugin:vite:vue] Whitespace was expected.
G:/v2/pd-xace-v2/xace-web/xh-web-vue3/src/views/project/template/phasePlanTemplate/ApplyToProjectModal.vue:52:27
50 |          <a-alert
51 |            message="警告"
52 |            description="选择"替换现有阶段计划"将会删除项目当前的所有阶段，请谨慎操作！"
   |                             ^
53 |            type="warning"
```

## 问题原因

在Vue模板的属性值中使用了中文引号（"和"），这会导致Vue编译器解析错误。Vue模板中的属性值必须使用英文引号（"或'）。

## 修复内容

### 修复的文件

1. **ApplyToProjectModal.vue** (phasePlanTemplate目录)
2. **ApplyToProjectModal.vue** (components目录)  
3. **zh-CN.ts** (语言包文件)

### 修复详情

#### 1. phasePlanTemplate/ApplyToProjectModal.vue

**修复前：**
```vue
description="选择"替换现有阶段计划"将会删除项目当前的所有阶段，请谨慎操作！"
```

**修复后：**
```vue
description="选择'替换现有阶段计划'将会删除项目当前的所有阶段，请谨慎操作！"
```

#### 2. components/ApplyToProjectModal.vue

**修复前：**
```vue
:message="`将向项目 "${selectedProject.projectName}" 应用模板 "${templateInfo.name}"`"
```

**修复后：**
```vue
:message="`将向项目 '${selectedProject.projectName}' 应用模板 '${templateInfo.name}'`"
```

#### 3. locales/zh-CN.ts

**修复前：**
```typescript
deleteConfirm: '确定要删除阶段模板"{name}"吗？',
previewInfo: '将向项目 "{projectName}" 应用模板 "{templateName}"',
```

**修复后：**
```typescript
deleteConfirm: '确定要删除阶段模板"{name}"吗？',
previewInfo: '将向项目 "{projectName}" 应用模板 "{templateName}"',
```

## 引号使用规范

### Vue模板中的引号规范

1. **属性值必须使用英文引号**
   ```vue
   <!-- ✅ 正确 -->
   <a-input placeholder="请输入内容" />
   <a-button :title="'点击按钮'" />
   
   <!-- ❌ 错误 -->
   <a-input placeholder="请输入内容" />
   ```

2. **模板字符串中的引号**
   ```vue
   <!-- ✅ 正确 -->
   :message="`将向项目 '${projectName}' 应用模板`"
   :message="`将向项目 \"${projectName}\" 应用模板`"
   
   <!-- ❌ 错误 -->
   :message="`将向项目 "${projectName}" 应用模板`"
   ```

3. **JavaScript字符串中的引号**
   ```typescript
   // ✅ 正确
   const message = '确定要删除"项目名称"吗？';
   const message = "确定要删除'项目名称'吗？";
   
   // ❌ 错误
   const message = '确定要删除"项目名称"吗？';
   ```

### 语言包中的引号规范

1. **中文语言包**
   - 对于参数占位符使用英文引号：`"{name}"`
   - 对于中文内容中的引用可以使用中文引号：`确定要删除"项目"吗？`

2. **英文语言包**
   - 统一使用英文引号：`"Are you sure to delete \"{name}\"?"`

## 检查和预防

### 1. 开发时检查
- 使用支持Vue语法高亮的编辑器
- 启用ESLint和Prettier检查
- 定期运行编译检查

### 2. 常见错误场景
- 复制粘贴中文内容到Vue模板
- 在模板字符串中直接使用中文引号
- 从Word或其他富文本编辑器复制内容

### 3. 修复工具
可以使用正则表达式批量查找和替换：

**查找中文引号：**
```regex
"([^"]*)"
```

**替换为英文引号：**
```
"$1"
```

## 修复结果

✅ 所有Vue模板编译错误已修复
✅ 引号使用规范已统一
✅ 项目可以正常编译和运行

## 注意事项

1. **Vue模板中永远不要使用中文引号**
2. **复制中文内容时要检查引号**
3. **使用模板字符串时注意嵌套引号的转义**
4. **定期运行编译检查以及时发现问题**

通过这次修复，项目的Vue模板语法错误已经全部解决，可以正常编译和运行了。
