<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinghuo.common.file.dao.FileDetailMapper">
  <resultMap id="BaseResultMap" type="com.xinghuo.common.file.entity.FileDetailEntity">
    <!--@mbg.generated-->
    <!--@Table base_file_detail-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="size" jdbcType="BIGINT" property="size" />
    <result column="filename" jdbcType="VARCHAR" property="filename" />
    <result column="original_filename" jdbcType="VARCHAR" property="originalFilename" />
    <result column="base_path" jdbcType="VARCHAR" property="basePath" />
    <result column="path" jdbcType="VARCHAR" property="path" />
    <result column="ext" jdbcType="VARCHAR" property="ext" />
    <result column="content_type" jdbcType="VARCHAR" property="contentType" />
    <result column="platform" jdbcType="VARCHAR" property="platform" />
    <result column="th_url" jdbcType="VARCHAR" property="thUrl" />
    <result column="th_filename" jdbcType="VARCHAR" property="thFilename" />
    <result column="th_size" jdbcType="BIGINT" property="thSize" />
    <result column="th_content_type" jdbcType="VARCHAR" property="thContentType" />
    <result column="object_id" jdbcType="VARCHAR" property="objectId" />
    <result column="object_type" jdbcType="VARCHAR" property="objectType" />
    <result column="attr" jdbcType="LONGVARCHAR" property="attr" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, url, `size`, filename, original_filename, base_path, `path`, ext, content_type,
    platform, th_url, th_filename, th_size, th_content_type, object_id, object_type,
    attr, create_time
  </sql>
</mapper>
