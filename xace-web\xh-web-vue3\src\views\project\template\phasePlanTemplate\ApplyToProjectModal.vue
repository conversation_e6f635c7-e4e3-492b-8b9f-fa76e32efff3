<template>
  <BasicModal v-bind="$attrs" @register="registerModal" title="应用模板到项目" @ok="handleSubmit">
    <div class="apply-modal-content">
      <div class="template-info">
        <h4>模板信息</h4>
        <div class="info-item">
          <span class="label">模板名称：</span>
          <span class="value">{{ templateRecord.name }}</span>
        </div>
        <div class="info-item">
          <span class="label">阶段数量：</span>
          <span class="value">{{ templateRecord.phaseCount || 0 }} 个</span>
        </div>
        <div class="info-item">
          <span class="label">总工期：</span>
          <span class="value">{{ templateRecord.totalDuration || 0 }} 天</span>
        </div>
      </div>

      <div class="project-selection">
        <h4>选择项目</h4>
        <a-select
          v-model:value="selectedProjectId"
          placeholder="请选择要应用模板的项目"
          style="width: 100%"
          show-search
          :filter-option="filterOption"
          @change="handleProjectChange"
        >
          <a-select-option
            v-for="project in projectList"
            :key="project.id"
            :value="project.id"
          >
            {{ project.code }} - {{ project.name }}
          </a-select-option>
        </a-select>
      </div>

      <div class="apply-options" v-if="selectedProjectId">
        <h4>应用选项</h4>
        <a-checkbox-group v-model:value="applyOptions">
          <a-checkbox value="replaceExisting">替换现有阶段计划</a-checkbox>
          <a-checkbox value="copyApprovalFlow">复制审批流程配置</a-checkbox>
          <a-checkbox value="copyCheckTemplate">复制检查单配置</a-checkbox>
        </a-checkbox-group>
      </div>

      <div class="warning-info" v-if="selectedProjectId && applyOptions.includes('replaceExisting')">
        <a-alert
          message="警告"
          description="选择'替换现有阶段计划'将会删除项目当前的所有阶段，请谨慎操作！"
          type="warning"
          show-icon
        />
      </div>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, reactive } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { applyPhasePlanTemplateToProject } from '/@/api/project/phasePlanTemplate';
  import { getProjectSelectList } from '/@/api/project/projectBase';
  import { useMessage } from '/@/hooks/web/useMessage';

  defineOptions({ name: 'ApplyToProjectModal' });

  const emit = defineEmits(['success', 'register']);
  const { createMessage } = useMessage();
  
  const templateRecord = ref<Recordable>({});
  const selectedProjectId = ref('');
  const projectList = ref<any[]>([]);
  const applyOptions = ref<string[]>(['copyApprovalFlow', 'copyCheckTemplate']);

  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    setModalProps({ confirmLoading: false });
    templateRecord.value = data.record;
    selectedProjectId.value = '';
    applyOptions.value = ['copyApprovalFlow', 'copyCheckTemplate'];
    
    // 加载项目列表
    try {
      const projects = await getProjectSelectList();
      projectList.value = projects || [];
    } catch (error) {
      createMessage.error('加载项目列表失败');
    }
  });

  function filterOption(input: string, option: any) {
    return option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  }

  function handleProjectChange(projectId: string) {
    selectedProjectId.value = projectId;
  }

  async function handleSubmit() {
    if (!selectedProjectId.value) {
      createMessage.warning('请选择要应用模板的项目');
      return;
    }

    try {
      setModalProps({ confirmLoading: true });

      const result = await applyPhasePlanTemplateToProject(
        templateRecord.value.id,
        selectedProjectId.value
      );

      createMessage.success('模板应用成功');
      closeModal();
      emit('success', result);
    } catch (error) {
      createMessage.error('模板应用失败');
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>

<style lang="less" scoped>
.apply-modal-content {
  .template-info,
  .project-selection,
  .apply-options {
    margin-bottom: 24px;

    h4 {
      font-size: 14px;
      font-weight: 600;
      margin-bottom: 12px;
      color: #262626;
    }

    .info-item {
      display: flex;
      margin-bottom: 8px;

      .label {
        width: 100px;
        color: #8c8c8c;
      }

      .value {
        color: #262626;
        font-weight: 500;
      }
    }
  }

  .warning-info {
    margin-top: 16px;
  }
}
</style>
