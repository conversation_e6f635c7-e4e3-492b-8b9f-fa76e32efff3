<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinghuo.permission.dao.PositionMapper">

    <select id="getListId" resultType="java.lang.String">
        SELECT F_Id from base_user WHERE F_EnabledMark = 1
    </select>

    <select id="query" resultType="java.lang.String">
        (
        SELECT F_Id FROM base_position WHERE F_OrganizeId = #{orgId}
        <if test="keyword != null and keyword != ''">
            AND (F_FullName LIKE #{keyword} OR F_EnCode LIKE #{keyword})
        </if>
        <!-- Oracle数据库不允许union前面使用排序，如使用MySQL则可以拷出到下面：ORDER BY F_SortCode ASC, F_CreatorTime DESC -->
        )
        UNION
        ( SELECT F_Id FROM base_position WHERE F_Id IN (
            SELECT DISTINCT a.F_Id FROM (
                SELECT
                bp.F_Id
                FROM
                base_organize bo
                LEFT JOIN
                base_organize_relation bor
                ON bo.F_Id = bor.F_Organize_Id
                LEFT JOIN
                base_position bp
                ON bp.F_Id = bor.F_Object_Id
                WHERE
                bor.F_Object_Type = 'Position'
                AND bor.F_Object_Type IS NOT NULL
                <if test="keyword != null and keyword != ''">
                    AND (bp.F_FullName LIKE #{keyword} OR bp.F_EnCode LIKE #{keyword})
                </if>
                <if test="orgIdList != null and orgIdList.size() != 0">
                    AND bo.F_Id IN
                    <foreach collection="orgIdList"
                             item="orgIds" index="index" separator="," open="(" close=")">
                        #{orgIds}
                    </foreach>
                </if>
                <!-- SQLServer数据库不允许union使用排序，如使用MySQL则可以拷出到下面：ORDER BY bp.F_SortCode ASC, bp.F_CreatorTime DESC -->
                ) a
            )
        )
    </select>

</mapper>
