# 应用服务器
server:
  tomcat:
    uri-encoding: UTF-8 #tomcat编码
  port: 30001 #tomcat端口
  forward-headers-strategy: framework #处理ngx+swagger3访问swagger-config找不到路径的问题

spring:
  devtools: #spring开发者工具模块
#    restart:
#      enabled: true #热部署开关
    freemarker:
      cache: false #spring内置freemarker缓存
  thymeleaf:
    cache: false #spring内置thymeleaf缓存

  # ===================== 数据源配置 =====================
  exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure #排除自动配置，手动配置druid
  datasource:
    db-type: MySQL #数据库类型(可选值 MySQL、SQLServer、Oracle、DM8、KingbaseES、PostgreSQL，请严格按可选值填写)
    host: **************
    port: 18830
    username: root
    password: My2sq3l_*paSs
    db-name: xace200_test
    url: jdbc:mysql://${spring.datasource.host}:${spring.datasource.port}/${spring.datasource.db-name}?useUnicode=true&characterEncoding=utf-8&useSSL=false&allowMultiQueries=true&rewriteBatchedStatements=true
    db-schema: #金仓达梦选填
    prepare-url: #自定义url

    # ===================== 动态多数据源 =====================
    dynamic:
      primary: master #设置默认的数据源或者数据源组,默认值即为master
      strict: true #严格匹配数据源,默认false. true未匹配到指定数据源时抛异常,false使用默认数据源
      druid:
        # 空闲时执行连接测试
        test-while-idle: true
        # 连接测试最小间隔
        time-between-eviction-runs-millis: 60000
        # 获取连接等待3秒 根据网络情况设定
        max-wait: 3000
        # 初始化4个连接
        initial-size: 4
        # 最大20个连接
        max-active: 20
        # 最少保持4个空闲连接
        min-idle: 4
        # 空闲连接保活, 超过配置的空闲时间会进行连接检查完成保活操作(数据库自身会断开达到空闲时间的连接， 程序使用断开的连接会报错)
        keep-alive: true
        filter:
          stat: # SQL统计过滤器配置
            enabled: true # 启用SQL统计过滤器
            log-slow-sql: true # 启用慢SQL日志记录
            slow-sql-millis: 10 # 慢SQL的执行时间阈值（单位：毫秒
          wall:
            enabled: true
            config:
              # 打开SQL执行耗时的详细信息
              wall-statistics: true
              # SQL执行超过该时间（毫秒）会被打印出来
              wall-sql-execute-millis: 10
        # 解除注释后Druid连接池打印SQL语句 忽略日志等级配置
        filters: slf4j,wall,stat
        slf4j:
          statementLogEnabled: true  #SQL语句的日志记录
          resultSetLogEnabled: false   #结果集的日志记录
          connectionLogEnabled: false  #数据库连接的日志记录
          dataSourceLogEnabled: false #数据源的日志记录
          statementCreateAfterLogEnabled: false #SQL语句创建后的日志记
          statementCloseAfterLogEnabled: false #SQL语句关闭后的日志记录
          statementExecuteAfterLogEnabled: false #SQL语句执行后的日志记录
          #打印SQL替换参数
          statementExecutableSqlLogEnable: true #可执行SQL语句的日志记录
          statementPrepareAfterLogEnabled: false  #SQL语句准备后的日志记录
          statementPrepareCallAfterLogEnabled: false #SQL语句准备调用后的日志记录
          statementParameterSetLogEnabled: false #SQL语句参数设置的日志记录
  #      datasource:
  #        master:
  #          url: jdbc:mysql://${spring.datasource.host}:${spring.datasource.port}/${spring.datasource.dbname}?useUnicode=true&characterEncoding=utf-8&useSSL=false&allowMultiQueries=true&serverTimezone=UTC
  #          username: ${spring.datasource.username}
  #          password: ${spring.datasource.password}
  #          driver-class-name: com.mysql.cj.jdbc.Driver
  cache:
    type: redis
  # ===================== Redis配置 =====================
  # redis单机模式
  data:
    redis:
      database: 6 #缓存库编号
      host: 127.0.0.1
      port: 8001
      password: RedisXHGig123@pass    # 密码为空时，请将本行注释
      timeout: 3000 #超时时间(单位：秒)
      lettuce: #Lettuce为Redis的Java驱动包
        pool:
          max-active: 50 # 连接池最大连接数
          max-wait: 60000  # 连接池最大阻塞等待时间（使用负值表示没有限制）
          min-idle: 10 # 连接池中的最小空闲连接
          max-idle: 50 # 连接池中的最大空闲连接

# redis集群模式
#  redis:
#    cluster:
#      nodes:
#        - *************:6380
#        - *************:6381
#        - *************:6382
#        - *************:6383
#        - *************:6384
#        - *************:6385
#    password: 123456 # 密码为空时，请将本行注释
#    timeout: 3000 # 超时时间(单位：秒)
#    lettuce: #Lettuce为Redis的Java驱动包
#      pool:
#        max-active: 8 # 连接池最大连接数
#        max-wait: -1ms  # 连接池最大阻塞等待时间（使用负值表示没有限制）
#        min-idle: 0 # 连接池中的最小空闲连接
#        max-idle: 8 # 连接池中的最大空闲连接
jetcache:
  statIntervalMinutes: 15
  areaInCacheName: false
  local:
    default:
      type: caffeine
      keyConvertor: jackson
      limit: 100
      expireAfterWriteInMillis: 100000
  remote:
    default:
      type: redis.springdata
      keyConvertor: jackson
      broadcastChannel: projectA
      keyPrefix: projectA
      valueEncoder: java
      valueDecoder: java
      defaultExpireInMillis: 86400000  #一天
# SpringDoc接口文档 访问地址：http://127.0.0.1:30000/doc.html  如果后台被nginx代理，记得配置 X-forwarded-Prefix 参数
springdoc:
  default-flat-param-object: true
  api-docs:
    enabled: true
    path: /v3/api-docs
  group-configs:
    - group: 绩效打分
      packages-to-scan: com.xinghuo.checkscore
    - group: 工时管理
      packages-to-scan: com.xinghuo.manhour
    - group: 业务模块
      packages-to-scan: com.xinghuo.modules
    - group: 对外接口
      packages-to-scan: com.xinghuo.outer
    - group: 系统管理
      packages-to-scan: com.xinghuo.system,com.xinghuo.oauth,com.xinghuo.oauth,com.xinghuo.exception,com.xinghuo.permission,com.xinghuo.scheduletask,com.xinghuo.message
    - group: 工作流
      packages-to-scan: com.xinghuo.workflow,com.xinghuo.form
    - group: 在线开发(大屏、报表、在线表单) 包路径
      packages-to-scan: com.xinghuo.visualdata,com.xinghuo.visualdev
    - group: 案例、扩展应用、APP应用 包路径
      packages-to-scan: com.xinghuo.example,com.xinghuo.extend,com.xinghuo.app
config:
  # ===================== 是否开启测试环境 =====================
  TestVersion: false
  # ===================== ApacheShardingSphere 配置开关 =====================
  sharding-sphere-enabled: false
  # ===================== 文件存储配置 =====================
dromara:
  x-file-storage: #文件存储配置
    default-platform: local-plus-1 #默认使用的存储平台
    thumbnail-suffix: ".min.jpg" #缩略图后缀，例如【.min.jpg】【.png】
    local-plus: # 本地存储升级版
      - platform: local-plus-1 # 存储平台标识
        enable-storage: true  #启用存储
        enable-access: true #启用访问（线上请使用 Nginx 配置，效率更高）
        domain: "" # 访问域名，例如：“http://127.0.0.1:8030/”，注意后面要和 path-patterns 保持一致，“/”结尾，本地存储建议使用相对路径，方便后期更换域名
     #   base-path: /data/xace200/xh-admin/xace-resources/ # 基础路径
        base-path:  # 基础路径
        path-patterns: /** # 访问路径
        storage-path: G:/v2/pd-xace-v2/xace-service/xace-resources/  # 存储路径
    aliyun-oss: # 阿里云 OSS ，不使用的情况下可以不写
      - platform: aliyun-oss-1 # 存储平台标识
        enable-storage: false  # 启用存储
        access-key: ??
        secret-key: ??
        end-point: ??
        bucket-name: ??
        domain: ?? # 访问域名，注意“/”结尾，例如：https://abc.oss-cn-shanghai.aliyuncs.com/
        base-path: hy/ # 基础路径
    qiniu-kodo: # 七牛云 kodo ，不使用的情况下可以不写
      - platform: qiniu-kodo-1 # 存储平台标识
        enable-storage: false  # 启用存储
        access-key: ??
        secret-key: ??
        bucket-name: ??
        domain: ?? # 访问域名，注意“/”结尾，例如：http://abc.hn-bkt.clouddn.com/
        base-path: base/ # 基础路径
    tencent-cos: # 腾讯云 COS
      - platform: tencent-cos-1 # 存储平台标识
        enable-storage: false  # 启用存储
        secret-id: ??
        secret-key: ??
        region: ?? #存仓库所在地域
        bucket-name: ??
        domain: ?? # 访问域名，注意“/”结尾，例如：https://abc.cos.ap-nanjing.myqcloud.com/
        base-path: hy/ # 基础路径
    minio: # MinIO，由于 MinIO SDK 支持 AWS S3，其它兼容 AWS S3 协议的存储平台也都可配置在这里
      - platform: minio-1 # 存储平台标识
        enable-storage: false  # 启用存储
        access-key: Q9jJs2b6Tv
        secret-key: Thj2WkpLu9DhmJyJ
        end-point: http://192.168.0.207:9000/
        bucket-name: xacesoftoss
        domain:  # 访问域名，注意“/”结尾，例如：http://minio.abc.com/abc/
        base-path:  # 基础路径

# ===================== 第三方登录配置 =====================
socials:
  # 第三方登录功能开关(false-关闭，true-开启)
  socials-enabled: false
  config:
    - # 微信
      provider: wechat_open
      client-id: your-client-id
      client-secret: your-client-secret
    - # qq
      provider: qq
      client-id: your-client-id
      client-secret: your-client-secret
    - # 企业微信
      provider: wechat_enterprise
      client-id: your-client-id
      client-secret: your-client-secret
      agentId: your-agentId
    - # 钉钉
      provider: dingtalk
      client-id: your-client-id
      client-secret: your-client-secret
      agentId: your-agentId
    - # 飞书
      provider: feishu
      client-id: your-client-id
      client-secret: your-client-secret
    - # 小程序
      provider: wechat_applets
      client-id: your-client-id
      client-secret: your-client-secret
    - # 智能桌面
      provider: sso
      client-id: frameworkservice
      client-secret: d2380065e19a63dd33507c5633363c9b
# ===================== 接口放行地址 与GatewayWhite中的默认URL合并 =====================
gateway:
  #禁止访问(主要是swagger,magicapi,actuator相关的路径)
  block-url:
  #不验证Token, 记录访问
  white-url:
  #  - /api/message/Notice
  #  - /api/permission/Users/<USER>
  #放行不记录(websocket路径..)
  exclude-url:
  #禁止访问地址的白名单IP。正式环境必须配置IP才可访问magicapi,doc.html
  white-ip:
    #  - *************
      - 192.168
      - 127.0.0.1
# ===================== 任务调度配置 =====================
xxl:
  job:
    accessToken: ''
    i18n: zh_CN
    logretentiondays: 30
    triggerpool:
      fast:
        max: 200
      slow:
        max: 100
    # xxl-job服务端地址
    admin:
      addresses: http://127.0.0.1:30020/xxl-job-admin/
    executor:
      address: ''
      appname: xxl-job-executor-sample1
      ip: ''
      logpath: /data/applogs/xxl-job/jobhandler
      logretentiondays: 30
      port: 9999
  # rest调用xxl-job接口地址
  admin:
    register:
      handle-query-address: ${xxl.job.admin.addresses}api/handler/queryList
      job-info-address: ${xxl.job.admin.addresses}api/jobinfo
      log-query-address: ${xxl.job.admin.addresses}api/log
      task-list-address: ${xxl.job.admin.addresses}api/ScheduleTask/List
      task-info-address: ${xxl.job.admin.addresses}api/ScheduleTask/getInfo
      task-save-address: ${xxl.job.admin.addresses}api/ScheduleTask
      task-update-address: ${xxl.job.admin.addresses}api/ScheduleTask
      task-remove-address: ${xxl.job.admin.addresses}api/ScheduleTask/remove
      task-start-or-remove-address: ${xxl.job.admin.addresses}api/ScheduleTask/updateTask
# Actuator
management:
  endpoints:
    web:
      exposure:
        include: "*"  # 公开所有端点，或指定以逗号分隔的端点id列表
  endpoint:
    health:
      show-details: always  # 显示详细的运行状况信息