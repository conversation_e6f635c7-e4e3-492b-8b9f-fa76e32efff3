<template>
  <BasicModal 
    v-bind="$attrs" 
    @register="registerModal" 
    title="交付物计划模板详情" 
    :footer="null"
    width="1200px"
  >
    <div class="template-detail">
      <!-- 基本信息 -->
      <div class="detail-section">
        <h3 class="section-title">基本信息</h3>
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="模板名称">{{ templateInfo.name }}</a-descriptions-item>
          <a-descriptions-item label="知识状态">
            <a-tag :color="getStatusColor(templateInfo.knStatusId)">
              {{ getStatusText(templateInfo.knStatusId) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="模板描述" :span="2">{{ templateInfo.description || '暂无描述' }}</a-descriptions-item>
          <a-descriptions-item label="交付物总数">{{ templateInfo.workProductCount || 0 }}</a-descriptions-item>
          <a-descriptions-item label="需评审数量">{{ templateInfo.reviewRequiredCount || 0 }}</a-descriptions-item>
          <a-descriptions-item label="最终交付成果数量">{{ templateInfo.deliverableCount || 0 }}</a-descriptions-item>
          <a-descriptions-item label="关联项目模板数">{{ templateInfo.projectTemplateCount || 0 }}</a-descriptions-item>
          <a-descriptions-item label="创建时间">{{ templateInfo.createdAt }}</a-descriptions-item>
          <a-descriptions-item label="创建人">{{ templateInfo.creatorUserName }}</a-descriptions-item>
        </a-descriptions>
      </div>

      <!-- 交付物明细 -->
      <div class="detail-section">
        <h3 class="section-title">交付物明细</h3>
        <BasicTable @register="registerTable" />
      </div>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicTable, useTable } from '/@/components/Table';
  import { detailColumns } from './workProductPlanTemplate.data';
  import { getWorkProductPlanTemplateDetailInfo } from '/@/api/project/workProductPlanTemplate';

  defineOptions({ name: 'WorkProductPlanTemplateDetailModal' });

  const templateInfo = ref<any>({});

  const [registerTable, { setTableData }] = useTable({
    title: '交付物明细列表',
    columns: detailColumns,
    pagination: false,
    showTableSetting: false,
    bordered: true,
    size: 'small',
  });

  const [registerModal, { setModalProps }] = useModalInner(async (data) => {
    setModalProps({ confirmLoading: true });
    
    try {
      if (data?.record) {
        const result = await getWorkProductPlanTemplateDetailInfo(data.record.id);
        templateInfo.value = result.data || result;
        
        // 设置交付物明细数据
        const workProducts = templateInfo.value.workProducts || [];
        setTableData(workProducts);
      }
    } catch (error) {
      console.error('获取模板详情失败:', error);
    } finally {
      setModalProps({ confirmLoading: false });
    }
  });

  function getStatusColor(status: string) {
    const colorMap = {
      'draft': 'warning',
      'published': 'success',
      'archived': 'default',
    };
    return colorMap[status] || 'default';
  }

  function getStatusText(status: string) {
    const textMap = {
      'draft': '未发布',
      'published': '已发布',
      'archived': '已归档',
    };
    return textMap[status] || status;
  }
</script>

<style lang="less" scoped>
  .template-detail {
    .detail-section {
      margin-bottom: 24px;
      
      .section-title {
        margin-bottom: 16px;
        font-size: 16px;
        font-weight: 600;
        color: #262626;
        border-left: 4px solid #1890ff;
        padding-left: 12px;
      }
    }
  }
</style>
