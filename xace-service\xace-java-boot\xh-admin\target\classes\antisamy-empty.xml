<?xml version="1.0" encoding="ISO-8859-1" ?>


<!--
W3C rules retrieved from:
http://www.w3.org/TR/html401/struct/global.html
-->


<anti-samy-rules xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		xsi:noNamespaceSchemaLocation="antisamy.xsd">

	<directives>
		<directive name="omitXmlDeclaration" value="true"/>
		<directive name="omitDoctypeDeclaration" value="true"/>
		<directive name="maxInputSize" value="0"/>
		<directive name="useXHTML" value="true"/>
		<directive name="formatOutput" value="false"/>

		<!--
		remember, this won't work for relative URIs - AntiSamy doesn't
		know anything about the URL or your web structure
		-->
		<directive name="embedStyleSheets" value="false"/>

	</directives>

	<common-regexps>
	</common-regexps>

	<!--

	Tag.name = a, b, div, body, etc.
	Tag.action = filter: remove tags, but keep content, validate: keep content as long as it passes rules, remove: remove tag and contents
	Attribute.name = id, class, href, align, width, etc.
	Attribute.onInvalid = what to do when the attribute is invalid, e.g., remove the tag (removeTag), remove the attribute (removeAttribute), filter the tag (filterTag)
	Attribute.description = What rules in English you want to tell the users they can have for this attribute. Include helpful things so they'll be able to tune their HTML

	 -->

	<!--
	Some attributes are common to all (or most) HTML tags. There aren't many that qualify for this. You have to make sure there's no
	collisions between any of these attribute names with attribute names of other tags that are for different purposes.
	-->

	<common-attributes>
	</common-attributes>


	<!--
	This requires normal updates as browsers continue to diverge from the W3C and each other. As long as the browser wars continue
	this is going to continue. I'm not sure war is the right word for what's going on. Doesn't somebody have to win a war after
	a while? Even wars of attrition, surely?
	 -->

	<global-tag-attributes>
	</global-tag-attributes>

	<tags-to-encode>
	</tags-to-encode>

	<tag-rules>
	</tag-rules>


	<!--  CSS validation processing rules  -->

	<css-rules>
		<!--  end manual properties -->
	</css-rules>
	<allowed-empty-tags>
    </allowed-empty-tags>
</anti-samy-rules>
