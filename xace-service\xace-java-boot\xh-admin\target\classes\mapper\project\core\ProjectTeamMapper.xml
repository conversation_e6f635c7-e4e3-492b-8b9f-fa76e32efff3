<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinghuo.project.core.dao.ProjectTeamMapper">

    <!-- 项目团队基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.xinghuo.project.core.entity.ProjectTeamEntity">
        <id column="f_id" property="id" jdbcType="VARCHAR"/>
        <result column="project_id" property="projectId" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="role_id" property="roleId" jdbcType="VARCHAR"/>
        <result column="role_name" property="roleName" jdbcType="VARCHAR"/>
        <result column="is_main_responsible" property="isMainResponsible" jdbcType="BOOLEAN"/>
        <result column="join_time" property="joinTime" jdbcType="TIMESTAMP"/>
        <result column="leave_time" property="leaveTime" jdbcType="TIMESTAMP"/>
        <result column="team_status" property="teamStatus" jdbcType="INTEGER"/>
        <result column="workload_percent" property="workloadPercent" jdbcType="DOUBLE"/>
        <result column="duty_desc" property="dutyDesc" jdbcType="VARCHAR"/>
        <result column="contact_info" property="contactInfo" jdbcType="VARCHAR"/>
        <result column="remarks" property="remarks" jdbcType="VARCHAR"/>
        <result column="f_created_at" property="createdAt" jdbcType="TIMESTAMP"/>
        <result column="f_updated_at" property="updatedAt" jdbcType="TIMESTAMP"/>
        <result column="f_created_by" property="createdBy" jdbcType="VARCHAR"/>
        <result column="f_updated_by" property="updatedBy" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 项目团队DTO结果映射 -->
    <resultMap id="TeamDTOResultMap" type="com.xinghuo.project.core.model.dto.ProjectTeamDTO" extends="BaseResultMap">
        <result column="user_name" property="userName" jdbcType="VARCHAR"/>
        <result column="display_name" property="displayName" jdbcType="VARCHAR"/>
        <result column="department_id" property="departmentId" jdbcType="VARCHAR"/>
        <result column="department_name" property="departmentName" jdbcType="VARCHAR"/>
        <result column="department_code" property="departmentCode" jdbcType="VARCHAR"/>
        <result column="avatar_url" property="avatarUrl" jdbcType="VARCHAR"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="phone" property="phone" jdbcType="VARCHAR"/>
        <result column="position" property="position" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础SQL片段 -->
    <sql id="Base_Column_List">
        pt.f_id, pt.project_id, pt.user_id, pt.role_id, pt.role_name, pt.is_main_responsible,
        pt.join_time, pt.leave_time, pt.team_status, pt.workload_percent, pt.duty_desc,
        pt.contact_info, pt.remarks, pt.f_created_at, pt.f_updated_at, pt.f_created_by, pt.f_updated_by
    </sql>

    <!-- 根据项目ID查询团队成员列表 -->
    <select id="selectByProjectId" resultMap="TeamDTOResultMap">
        SELECT 
            <include refid="Base_Column_List"/>,
            u.f_name as user_name,
            u.f_display_name as display_name,
            u.f_department_id as department_id,
            d.f_name as department_name,
            d.f_code as department_code,
            u.f_avatar_url as avatar_url,
            u.f_email as email,
            u.f_phone as phone,
            u.f_position as position
        FROM zz_proj_team pt
        LEFT JOIN sys_user u ON pt.user_id = u.f_id
        LEFT JOIN sys_department d ON u.f_department_id = d.f_id
        WHERE pt.project_id = #{projectId}
        ORDER BY pt.is_main_responsible DESC, pt.join_time ASC
    </select>

    <!-- 根据用户ID查询参与的项目列表 -->
    <select id="selectProjectIdsByUserId" resultType="string">
        SELECT DISTINCT project_id
        FROM zz_proj_team
        WHERE user_id = #{userId} AND team_status = 1
        ORDER BY join_time DESC
    </select>

    <!-- 根据项目ID和用户ID查询团队成员信息 -->
    <select id="selectByProjectIdAndUserId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM zz_proj_team pt
        WHERE pt.project_id = #{projectId} AND pt.user_id = #{userId}
    </select>

    <!-- 根据项目ID和角色ID查询团队成员列表 -->
    <select id="selectByProjectIdAndRoleId" resultMap="TeamDTOResultMap">
        SELECT 
            <include refid="Base_Column_List"/>,
            u.f_name as user_name,
            u.f_display_name as display_name,
            u.f_department_id as department_id,
            d.f_name as department_name,
            d.f_code as department_code,
            u.f_avatar_url as avatar_url,
            u.f_email as email,
            u.f_phone as phone,
            u.f_position as position
        FROM zz_proj_team pt
        LEFT JOIN sys_user u ON pt.user_id = u.f_id
        LEFT JOIN sys_department d ON u.f_department_id = d.f_id
        WHERE pt.project_id = #{projectId} AND pt.role_id = #{roleId}
        ORDER BY pt.join_time ASC
    </select>

    <!-- 查询项目经理列表 -->
    <select id="selectManagersByProjectId" resultMap="TeamDTOResultMap">
        SELECT 
            <include refid="Base_Column_List"/>,
            u.f_name as user_name,
            u.f_display_name as display_name,
            u.f_department_id as department_id,
            d.f_name as department_name,
            d.f_code as department_code,
            u.f_avatar_url as avatar_url,
            u.f_email as email,
            u.f_phone as phone,
            u.f_position as position
        FROM zz_proj_team pt
        LEFT JOIN sys_user u ON pt.user_id = u.f_id
        LEFT JOIN sys_department d ON u.f_department_id = d.f_id
        WHERE pt.project_id = #{projectId} 
        AND (pt.role_name LIKE '%经理%' OR pt.role_name LIKE '%manager%' OR pt.is_main_responsible = 1)
        ORDER BY pt.is_main_responsible DESC, pt.join_time ASC
    </select>

    <!-- 查询主要负责人列表 -->
    <select id="selectMainResponsibleByProjectId" resultMap="TeamDTOResultMap">
        SELECT 
            <include refid="Base_Column_List"/>,
            u.f_name as user_name,
            u.f_display_name as display_name,
            u.f_department_id as department_id,
            d.f_name as department_name,
            d.f_code as department_code,
            u.f_avatar_url as avatar_url,
            u.f_email as email,
            u.f_phone as phone,
            u.f_position as position
        FROM zz_proj_team pt
        LEFT JOIN sys_user u ON pt.user_id = u.f_id
        LEFT JOIN sys_department d ON u.f_department_id = d.f_id
        WHERE pt.project_id = #{projectId} AND pt.is_main_responsible = 1
        ORDER BY pt.join_time ASC
    </select>

    <!-- 批量插入团队成员 -->
    <insert id="batchInsert">
        INSERT INTO zz_proj_team (
            f_id, project_id, user_id, role_id, role_name, is_main_responsible,
            join_time, team_status, workload_percent, duty_desc, contact_info, remarks,
            f_created_at, f_created_by
        ) VALUES
        <foreach collection="teamMembers" item="item" separator=",">
            (
                #{item.id}, #{item.projectId}, #{item.userId}, #{item.roleId}, #{item.roleName},
                #{item.isMainResponsible}, #{item.joinTime}, #{item.teamStatus}, #{item.workloadPercent},
                #{item.dutyDesc}, #{item.contactInfo}, #{item.remarks}, NOW(), #{item.createdBy}
            )
        </foreach>
    </insert>

    <!-- 根据项目ID删除所有团队成员 -->
    <delete id="deleteByProjectId">
        DELETE FROM zz_proj_team WHERE project_id = #{projectId}
    </delete>

    <!-- 更新团队成员状态 -->
    <update id="updateTeamStatus">
        UPDATE zz_proj_team 
        SET team_status = #{teamStatus}, f_updated_at = NOW()
        WHERE f_id = #{id}
    </update>

    <!-- 更新团队成员工作量 -->
    <update id="updateWorkloadPercent">
        UPDATE zz_proj_team 
        SET workload_percent = #{workloadPercent}, f_updated_at = NOW()
        WHERE f_id = #{id}
    </update>

    <!-- 检查用户是否为项目成员 -->
    <select id="checkUserInProject" resultType="int">
        SELECT COUNT(1)
        FROM zz_proj_team
        WHERE project_id = #{projectId} AND user_id = #{userId} AND team_status = 1
    </select>

    <!-- 检查用户是否为项目经理 -->
    <select id="checkUserIsManager" resultType="int">
        SELECT COUNT(1)
        FROM zz_proj_team
        WHERE project_id = #{projectId} AND user_id = #{userId} 
        AND (role_name LIKE '%经理%' OR role_name LIKE '%manager%' OR is_main_responsible = 1)
        AND team_status = 1
    </select>

    <!-- 获取项目团队统计信息 -->
    <select id="getTeamStatistics" resultType="map">
        SELECT 
            COUNT(1) as total_members,
            COUNT(CASE WHEN team_status = 1 THEN 1 END) as active_members,
            COUNT(CASE WHEN is_main_responsible = 1 THEN 1 END) as main_responsible_count,
            AVG(workload_percent) as avg_workload_percent
        FROM zz_proj_team
        WHERE project_id = #{projectId}
    </select>

    <!-- 根据部门ID查询项目团队成员 -->
    <select id="selectByDepartmentId" resultMap="TeamDTOResultMap">
        SELECT 
            <include refid="Base_Column_List"/>,
            u.f_name as user_name,
            u.f_display_name as display_name,
            u.f_department_id as department_id,
            d.f_name as department_name,
            d.f_code as department_code,
            u.f_avatar_url as avatar_url,
            u.f_email as email,
            u.f_phone as phone,
            u.f_position as position
        FROM zz_proj_team pt
        LEFT JOIN sys_user u ON pt.user_id = u.f_id
        LEFT JOIN sys_department d ON u.f_department_id = d.f_id
        WHERE u.f_department_id = #{departmentId}
        ORDER BY pt.join_time DESC
    </select>

    <!-- 查询用户在指定时间段内参与的项目 -->
    <select id="selectProjectIdsByUserIdAndTimeRange" resultType="string">
        SELECT DISTINCT project_id
        FROM zz_proj_team
        WHERE user_id = #{userId}
        AND join_time >= #{startTime}
        AND (leave_time IS NULL OR leave_time <= #{endTime})
        ORDER BY join_time DESC
    </select>

    <!-- 更新团队成员离开时间 -->
    <update id="updateLeaveTime">
        UPDATE zz_proj_team 
        SET leave_time = #{leaveTime}, team_status = 3, f_updated_at = NOW()
        WHERE f_id = #{id}
    </update>

    <!-- 查询活跃的团队成员 -->
    <select id="selectActiveTeamMembers" resultMap="TeamDTOResultMap">
        SELECT 
            <include refid="Base_Column_List"/>,
            u.f_name as user_name,
            u.f_display_name as display_name,
            u.f_department_id as department_id,
            d.f_name as department_name,
            d.f_code as department_code,
            u.f_avatar_url as avatar_url,
            u.f_email as email,
            u.f_phone as phone,
            u.f_position as position
        FROM zz_proj_team pt
        LEFT JOIN sys_user u ON pt.user_id = u.f_id
        LEFT JOIN sys_department d ON u.f_department_id = d.f_id
        WHERE pt.project_id = #{projectId} AND pt.team_status = 1
        ORDER BY pt.is_main_responsible DESC, pt.join_time ASC
    </select>

    <!-- 根据角色名称查询团队成员 -->
    <select id="selectByProjectIdAndRoleName" resultMap="TeamDTOResultMap">
        SELECT 
            <include refid="Base_Column_List"/>,
            u.f_name as user_name,
            u.f_display_name as display_name,
            u.f_department_id as department_id,
            d.f_name as department_name,
            d.f_code as department_code,
            u.f_avatar_url as avatar_url,
            u.f_email as email,
            u.f_phone as phone,
            u.f_position as position
        FROM zz_proj_team pt
        LEFT JOIN sys_user u ON pt.user_id = u.f_id
        LEFT JOIN sys_department d ON u.f_department_id = d.f_id
        WHERE pt.project_id = #{projectId} AND pt.role_name = #{roleName}
        ORDER BY pt.join_time ASC
    </select>

</mapper>
