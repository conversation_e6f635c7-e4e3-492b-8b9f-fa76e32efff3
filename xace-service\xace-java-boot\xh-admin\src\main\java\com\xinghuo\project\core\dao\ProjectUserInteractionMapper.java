package com.xinghuo.project.core.dao;

import com.xinghuo.common.base.dao.XHBaseMapper;
import com.xinghuo.project.core.entity.ProjectUserInteractionEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 用户项目交互Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Mapper
public interface ProjectUserInteractionMapper extends XHBaseMapper<ProjectUserInteractionEntity> {

    /**
     * 根据用户ID和交互类型查询项目ID列表
     *
     * @param userId 用户ID
     * @param interactionType 交互类型
     * @param interactionStatus 交互状态
     * @return 项目ID列表
     */
    List<String> selectProjectIdsByUserIdAndType(@Param("userId") String userId, 
                                                 @Param("interactionType") String interactionType,
                                                 @Param("interactionStatus") Integer interactionStatus);

    /**
     * 根据用户ID查询最近访问的项目
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @param days 最近天数
     * @return 项目ID列表（按访问时间倒序）
     */
    List<String> selectRecentlyVisitedProjects(@Param("userId") String userId, 
                                              @Param("limit") Integer limit,
                                              @Param("days") Integer days);

    /**
     * 根据项目ID和用户ID查询交互记录
     *
     * @param projectId 项目ID
     * @param userId 用户ID
     * @param interactionType 交互类型
     * @return 交互记录
     */
    ProjectUserInteractionEntity selectByProjectIdAndUserIdAndType(@Param("projectId") String projectId,
                                                                   @Param("userId") String userId,
                                                                   @Param("interactionType") String interactionType);

    /**
     * 更新或插入交互记录
     *
     * @param entity 交互记录
     * @return 影响行数
     */
    int insertOrUpdate(ProjectUserInteractionEntity entity);

    /**
     * 更新交互状态
     *
     * @param projectId 项目ID
     * @param userId 用户ID
     * @param interactionType 交互类型
     * @param interactionStatus 交互状态
     * @return 更新数量
     */
    int updateInteractionStatus(@Param("projectId") String projectId,
                               @Param("userId") String userId,
                               @Param("interactionType") String interactionType,
                               @Param("interactionStatus") Integer interactionStatus);

    /**
     * 增加交互次数
     *
     * @param projectId 项目ID
     * @param userId 用户ID
     * @param interactionType 交互类型
     * @return 更新数量
     */
    int incrementInteractionCount(@Param("projectId") String projectId,
                                 @Param("userId") String userId,
                                 @Param("interactionType") String interactionType);

    /**
     * 获取项目的交互统计
     *
     * @param projectId 项目ID
     * @return 统计信息
     */
    Map<String, Object> getProjectInteractionStatistics(@Param("projectId") String projectId);

    /**
     * 获取用户的交互统计
     *
     * @param userId 用户ID
     * @return 统计信息
     */
    Map<String, Object> getUserInteractionStatistics(@Param("userId") String userId);

    /**
     * 根据项目ID查询关注用户列表
     *
     * @param projectId 项目ID
     * @return 用户ID列表
     */
    List<String> selectFollowersByProjectId(@Param("projectId") String projectId);

    /**
     * 根据项目ID查询收藏用户列表
     *
     * @param projectId 项目ID
     * @return 用户ID列表
     */
    List<String> selectFavoriteUsersByProjectId(@Param("projectId") String projectId);

    /**
     * 检查用户是否关注项目
     *
     * @param projectId 项目ID
     * @param userId 用户ID
     * @return 是否关注
     */
    int checkUserFollowProject(@Param("projectId") String projectId, @Param("userId") String userId);

    /**
     * 检查用户是否收藏项目
     *
     * @param projectId 项目ID
     * @param userId 用户ID
     * @return 是否收藏
     */
    int checkUserFavoriteProject(@Param("projectId") String projectId, @Param("userId") String userId);

    /**
     * 删除用户的所有交互记录
     *
     * @param userId 用户ID
     * @return 删除数量
     */
    int deleteByUserId(@Param("userId") String userId);

    /**
     * 删除项目的所有交互记录
     *
     * @param projectId 项目ID
     * @return 删除数量
     */
    int deleteByProjectId(@Param("projectId") String projectId);

    /**
     * 获取热门项目（基于访问次数）
     *
     * @param limit 限制数量
     * @param days 统计天数
     * @return 项目ID列表
     */
    List<String> selectHotProjects(@Param("limit") Integer limit, @Param("days") Integer days);

    /**
     * 获取用户访问历史
     *
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 限制数量
     * @return 访问记录列表
     */
    List<ProjectUserInteractionEntity> selectUserVisitHistory(@Param("userId") String userId,
                                                              @Param("startTime") Date startTime,
                                                              @Param("endTime") Date endTime,
                                                              @Param("limit") Integer limit);

    /**
     * 清理过期的访问记录
     *
     * @param beforeDate 清理此日期之前的记录
     * @return 清理数量
     */
    int cleanExpiredVisitRecords(@Param("beforeDate") Date beforeDate);

    /**
     * 批量插入交互记录
     *
     * @param interactions 交互记录列表
     * @return 插入数量
     */
    int batchInsert(@Param("interactions") List<ProjectUserInteractionEntity> interactions);
}
