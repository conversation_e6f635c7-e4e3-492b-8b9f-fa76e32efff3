<template>
  <BasicDrawer
    v-bind="$attrs"
    @register="registerDrawer"
    title="阶段模板详情"
    width="70%"
    :canFullscreen="true"
    :maskClosable="true"
    :keyboard="true"
    class="phase-template-detail-drawer">
    <template #footer>
      <a-space>
        <a-button @click="closeDrawer">
          <template #icon><CloseOutlined /></template>
          关闭
        </a-button>
      </a-space>
    </template>

    <div class="phase-template-detail-container" v-loading="loading">
      <a-descriptions :column="2" bordered size="middle">
        <a-descriptions-item label="阶段编码">
          {{ phaseTemplateInfo?.code || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="阶段名称">
          {{ phaseTemplateInfo?.name || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="标准工期"> {{ phaseTemplateInfo?.stdDuration || 0 }} 天 </a-descriptions-item>
        <a-descriptions-item label="状态">
          <a-tag :color="phaseTemplateInfo?.status === 1 ? 'green' : 'red'">
            <CheckCircleOutlined v-if="phaseTemplateInfo?.status === 1" />
            <StopOutlined v-else />
            {{ phaseTemplateInfo?.status === 1 ? '启用' : '禁用' }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="默认审批流程">
          {{ phaseTemplateInfo?.defaultApprovalName || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="默认检查单">
          {{ phaseTemplateInfo?.defaultChecklistName || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="阶段描述" :span="2">
          <div class="description-content">
            {{ phaseTemplateInfo?.description || '暂无描述' }}
          </div>
        </a-descriptions-item>
        <a-descriptions-item label="创建时间">
          {{ formatDate(phaseTemplateInfo?.createdAt) }}
        </a-descriptions-item>
        <a-descriptions-item label="创建人">
          {{ phaseTemplateInfo?.createdBy || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="更新时间">
          {{ formatDate(phaseTemplateInfo?.updatedAt) }}
        </a-descriptions-item>
        <a-descriptions-item label="更新人">
          {{ phaseTemplateInfo?.updatedBy || '-' }}
        </a-descriptions-item>
      </a-descriptions>
    </div>
  </BasicDrawer>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { getPhaseTemplateInfo } from '/@/api/project/phaseTemplate';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { formatToDateTime } from '/@/utils/dateUtil';
  import { CloseOutlined, CheckCircleOutlined, StopOutlined } from '@ant-design/icons-vue';
  import { Descriptions } from 'ant-design-vue';
  const ADescriptions = Descriptions;
  const ADescriptionsItem = Descriptions.Item;
  const { createMessage } = useMessage();
  const phaseTemplateInfo = ref<any>({});
  const loading = ref(false);

  // 注册抽屉
  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async data => {
    try {
      setDrawerProps({ loading: true });
      phaseTemplateInfo.value = {};

      if (data?.id) {
        await loadPhaseTemplateDetail(data.id);
      }
    } catch (error) {
      console.error('初始化抽屉失败:', error);
      createMessage.error('加载详情失败');
    } finally {
      setDrawerProps({ loading: false });
    }
  });

  // 加载阶段模板详情
  async function loadPhaseTemplateDetail(id: string) {
    try {
      loading.value = true;
      const result = await getPhaseTemplateInfo(id);
      phaseTemplateInfo.value = result.data || {};
    } catch (error) {
      console.error('加载阶段模板详情失败:', error);
      createMessage.error('加载详情失败');
    } finally {
      loading.value = false;
    }
  }

  // 格式化日期
  function formatDate(date: string | Date | undefined) {
    if (!date) return '-';
    return formatToDateTime(date);
  }
</script>

<style lang="less" scoped>
  .phase-template-detail-container {
    padding: 24px;
  }

  .description-content {
    color: #666;
    line-height: 1.6;
    white-space: pre-wrap;
    word-break: break-word;
  }
</style>
