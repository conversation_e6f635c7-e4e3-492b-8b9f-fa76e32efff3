package com.xinghuo.project.biz.service;


import com.xinghuo.common.base.service.BaseService;
import com.xinghuo.project.biz.entity.BizContractMoneyEntity;
import com.xinghuo.project.biz.model.bizContractMoney.BizContractMoneyForm;
import com.xinghuo.project.biz.model.bizContractMoney.BizContractMoneyPagination;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 合同收款服务接口
 */
public interface BizContractMoneyService extends BaseService<BizContractMoneyEntity> {

    /**
     * 获取合同收款列表
     *
     * @param pagination 分页查询条件
     * @return 合同收款列表
     */
    List<BizContractMoneyEntity> getList(BizContractMoneyPagination pagination);

    /**
     * 根据合同ID获取收款列表
     *
     * @param contractId 合同ID
     * @return 收款列表
     */
    List<BizContractMoneyEntity> getListByContractId(String contractId);

    /**
     * 获取合同收款详情
     *
     * @param id 合同收款ID
     * @return 合同收款详情
     */
    BizContractMoneyEntity getInfo(String id);

    /**
     * 创建合同收款
     *
     * @param entity 合同收款实体
     */
    void create(BizContractMoneyEntity entity);

    /**
     * 更新合同收款
     *
     * @param id     合同收款ID
     * @param entity 合同收款实体
     */
    void update(String id, BizContractMoneyEntity entity);

    /**
     * 删除合同收款
     *
     * @param id 合同收款ID
     */
    void delete(String id);

    /**
     * 更新收款状态
     *
     * @param id     合同收款ID
     * @param form   状态更新表单
     */
    void updateStatus(String id, BizContractMoneyForm form);

    /**
     * 登记开票
     *
     * @param id          合同收款ID
     * @param kaipiaoDate 开票日期
     * @param lastNote    备注
     */
    void registerInvoice(String id, Date kaipiaoDate, String lastNote);

    /**
     * 登记收款
     *
     * @param id          合同收款ID
     * @param shoukuanDate 收款日期
     * @param lastNote     备注
     */
    void registerPayment(String id, Date shoukuanDate, String lastNote);

    /**
     * 获取收款统计数据
     *
     * @return 收款统计数据
     */
    Map<String, Object> getStatistics();
}
