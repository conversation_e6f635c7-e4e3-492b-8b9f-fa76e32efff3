<template>
  <a-dropdown :trigger="['click']" placement="bottomRight">
    <a-button :loading="exporting">
      <Icon icon="ant-design:download-outlined" />
      导出
      <Icon icon="ant-design:down-outlined" />
    </a-button>
    
    <template #overlay>
      <a-menu @click="handleMenuClick">
        <a-menu-item key="excel">
          <Icon icon="ant-design:file-excel-outlined" />
          导出Excel
        </a-menu-item>
        <a-menu-item key="csv">
          <Icon icon="ant-design:file-text-outlined" />
          导出CSV
        </a-menu-item>
        <a-menu-item key="pdf" v-if="enablePdf">
          <Icon icon="ant-design:file-pdf-outlined" />
          导出PDF
        </a-menu-item>
        <a-menu-divider />
        <a-menu-item key="custom">
          <Icon icon="ant-design:setting-outlined" />
          自定义导出
        </a-menu-item>
      </a-menu>
    </template>
  </a-dropdown>

  <!-- 自定义导出配置弹窗 -->
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    title="自定义导出"
    width="600px"
    @ok="handleCustomExport"
  >
    <div class="export-config">
      <!-- 导出格式 -->
      <div class="config-section">
        <h4>导出格式</h4>
        <a-radio-group v-model:value="exportConfig.format">
          <a-radio value="excel">Excel (.xlsx)</a-radio>
          <a-radio value="csv">CSV (.csv)</a-radio>
          <a-radio value="pdf" v-if="enablePdf">PDF (.pdf)</a-radio>
        </a-radio-group>
      </div>

      <!-- 导出范围 -->
      <div class="config-section">
        <h4>导出范围</h4>
        <a-radio-group v-model:value="exportConfig.scope">
          <a-radio value="all">全部数据</a-radio>
          <a-radio value="current">当前页数据</a-radio>
          <a-radio value="selected" :disabled="!hasSelectedData">
            已选择数据 ({{ selectedCount }} 条)
          </a-radio>
          <a-radio value="filtered">筛选后数据</a-radio>
        </a-radio-group>
      </div>

      <!-- 导出列 -->
      <div class="config-section">
        <h4>导出列</h4>
        <div class="columns-config">
          <div class="columns-header">
            <a-checkbox
              :indeterminate="indeterminate"
              :checked="checkAll"
              @change="handleCheckAllChange"
            >
              全选
            </a-checkbox>
            <a-button type="link" size="small" @click="resetColumns">
              重置
            </a-button>
          </div>
          <a-checkbox-group v-model:value="exportConfig.columns" class="columns-list">
            <div
              v-for="column in availableColumns"
              :key="column.dataIndex"
              class="column-item"
            >
              <a-checkbox :value="column.dataIndex">
                {{ column.title }}
              </a-checkbox>
            </div>
          </a-checkbox-group>
        </div>
      </div>

      <!-- 高级选项 -->
      <div class="config-section">
        <h4>高级选项</h4>
        <a-space direction="vertical" style="width: 100%">
          <a-checkbox v-model:checked="exportConfig.includeHeader">
            包含表头
          </a-checkbox>
          <a-checkbox v-model:checked="exportConfig.includeIndex">
            包含序号
          </a-checkbox>
          <a-checkbox v-model:checked="exportConfig.includeTimestamp">
            包含导出时间
          </a-checkbox>
          <div class="filename-config">
            <span>文件名：</span>
            <a-input
              v-model:value="exportConfig.filename"
              placeholder="请输入文件名"
              style="width: 200px"
            />
          </div>
        </a-space>
      </div>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, computed, watch } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { Icon } from '/@/components/Icon';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useTemplateActions } from '../hooks/useTemplateActions';
  import * as XLSX from 'xlsx';

  interface Column {
    title: string;
    dataIndex: string;
    width?: number;
  }

  interface ExportConfig {
    format: 'excel' | 'csv' | 'pdf';
    scope: 'all' | 'current' | 'selected' | 'filtered';
    columns: string[];
    includeHeader: boolean;
    includeIndex: boolean;
    includeTimestamp: boolean;
    filename: string;
  }

  interface Props {
    data: any[];
    columns: Column[];
    selectedData?: any[];
    filteredData?: any[];
    enablePdf?: boolean;
    defaultFilename?: string;
    exportApi?: (params: any) => Promise<any>;
  }

  const props = withDefaults(defineProps<Props>(), {
    selectedData: () => [],
    filteredData: () => [],
    enablePdf: false,
    defaultFilename: 'export_data',
  });

  const emit = defineEmits<{
    export: [config: ExportConfig, data: any[]];
  }>();

  const { createMessage } = useMessage();
  const { handleExport } = useTemplateActions();

  // 状态
  const exporting = ref(false);
  const exportConfig = ref<ExportConfig>({
    format: 'excel',
    scope: 'all',
    columns: [],
    includeHeader: true,
    includeIndex: false,
    includeTimestamp: true,
    filename: props.defaultFilename,
  });

  // Modal
  const [registerModal, { openModal, closeModal }] = useModalInner();

  // 计算属性
  const availableColumns = computed(() => {
    return props.columns.filter(col => col.dataIndex !== 'action');
  });

  const selectedCount = computed(() => props.selectedData.length);
  const hasSelectedData = computed(() => selectedCount.value > 0);

  const checkAll = computed(() => {
    return exportConfig.value.columns.length === availableColumns.value.length;
  });

  const indeterminate = computed(() => {
    const selectedLength = exportConfig.value.columns.length;
    return selectedLength > 0 && selectedLength < availableColumns.value.length;
  });

  // 初始化导出列
  watch(
    () => props.columns,
    () => {
      exportConfig.value.columns = availableColumns.value.map(col => col.dataIndex);
    },
    { immediate: true }
  );

  // 方法
  async function handleMenuClick({ key }: { key: string }) {
    if (key === 'custom') {
      openModal();
    } else {
      await quickExport(key as 'excel' | 'csv' | 'pdf');
    }
  }

  async function quickExport(format: 'excel' | 'csv' | 'pdf') {
    const config: ExportConfig = {
      format,
      scope: 'all',
      columns: availableColumns.value.map(col => col.dataIndex),
      includeHeader: true,
      includeIndex: false,
      includeTimestamp: true,
      filename: `${props.defaultFilename}_${new Date().toISOString().slice(0, 10)}`,
    };

    await executeExport(config);
  }

  async function handleCustomExport() {
    await executeExport(exportConfig.value);
    closeModal();
  }

  async function executeExport(config: ExportConfig) {
    const result = await handleExport(async () => {
      // 获取要导出的数据
      const exportData = getExportData(config.scope);
      
      // 过滤列
      const filteredData = filterColumns(exportData, config.columns);
      
      // 添加序号
      const finalData = config.includeIndex 
        ? filteredData.map((item, index) => ({ 序号: index + 1, ...item }))
        : filteredData;

      // 根据格式导出
      switch (config.format) {
        case 'excel':
          await exportToExcel(finalData, config);
          break;
        case 'csv':
          await exportToCSV(finalData, config);
          break;
        case 'pdf':
          await exportToPDF(finalData, config);
          break;
      }

      emit('export', config, finalData);
      return true;
    });

    return result;
  }

  function getExportData(scope: string): any[] {
    switch (scope) {
      case 'current':
        return props.data;
      case 'selected':
        return props.selectedData;
      case 'filtered':
        return props.filteredData.length > 0 ? props.filteredData : props.data;
      case 'all':
      default:
        return props.data;
    }
  }

  function filterColumns(data: any[], columns: string[]): any[] {
    const columnMap = new Map(props.columns.map(col => [col.dataIndex, col.title]));
    
    return data.map(item => {
      const filteredItem: any = {};
      columns.forEach(colKey => {
        const title = columnMap.get(colKey) || colKey;
        filteredItem[title] = item[colKey] || '';
      });
      return filteredItem;
    });
  }

  async function exportToExcel(data: any[], config: ExportConfig) {
    const worksheet = XLSX.utils.json_to_sheet(data, {
      header: config.includeHeader ? undefined : [],
    });
    
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1');
    
    // 添加导出时间
    if (config.includeTimestamp) {
      const timestamp = new Date().toLocaleString();
      XLSX.utils.sheet_add_aoa(worksheet, [[`导出时间: ${timestamp}`]], { origin: -1 });
    }
    
    const filename = `${config.filename}.xlsx`;
    XLSX.writeFile(workbook, filename);
  }

  async function exportToCSV(data: any[], config: ExportConfig) {
    const worksheet = XLSX.utils.json_to_sheet(data);
    let csv = XLSX.utils.sheet_to_csv(worksheet);
    
    // 添加BOM以支持中文
    const BOM = '\uFEFF';
    csv = BOM + csv;
    
    // 添加导出时间
    if (config.includeTimestamp) {
      const timestamp = new Date().toLocaleString();
      csv = `导出时间: ${timestamp}\n${csv}`;
    }
    
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `${config.filename}.csv`;
    link.click();
    URL.revokeObjectURL(link.href);
  }

  async function exportToPDF(data: any[], config: ExportConfig) {
    // 这里可以集成PDF导出库，如jsPDF
    createMessage.info('PDF导出功能开发中...');
  }

  function handleCheckAllChange(e: any) {
    if (e.target.checked) {
      exportConfig.value.columns = availableColumns.value.map(col => col.dataIndex);
    } else {
      exportConfig.value.columns = [];
    }
  }

  function resetColumns() {
    exportConfig.value.columns = availableColumns.value.map(col => col.dataIndex);
  }
</script>

<style lang="less" scoped>
  .export-config {
    .config-section {
      margin-bottom: 24px;

      h4 {
        margin-bottom: 12px;
        font-weight: 600;
        color: #262626;
      }
    }

    .columns-config {
      border: 1px solid #d9d9d9;
      border-radius: 6px;
      padding: 12px;

      .columns-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        padding-bottom: 8px;
        border-bottom: 1px solid #f0f0f0;
      }

      .columns-list {
        max-height: 200px;
        overflow-y: auto;

        .column-item {
          display: block;
          margin-bottom: 8px;
        }
      }
    }

    .filename-config {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }
</style>
