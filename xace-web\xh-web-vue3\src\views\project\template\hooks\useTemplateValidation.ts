import { ref, computed } from 'vue';
import { ValidationUtils } from '../utils/templateUtils';

/**
 * 模板表单验证Hook
 * 提供统一的表单验证规则和方法
 */
export function useTemplateValidation() {
  
  // 验证状态
  const validationErrors = ref<Record<string, string>>({});
  const isValidating = ref(false);

  /**
   * 通用验证规则
   */
  const commonRules = {
    // 必填验证
    required: (message?: string) => ({
      required: true,
      message: message || '此字段为必填项',
      trigger: ['blur', 'change'],
    }),

    // 编码验证
    code: (required = true) => {
      const rules: any[] = [];
      
      if (required) {
        rules.push({
          required: true,
          message: '请输入编码',
          trigger: 'blur',
        });
      }
      
      rules.push({
        validator: (_rule: any, value: string) => {
          if (!value && !required) return Promise.resolve();
          if (!ValidationUtils.validateCode(value)) {
            return Promise.reject('编码格式不正确（字母开头，可包含字母、数字、下划线，长度3-20）');
          }
          return Promise.resolve();
        },
        trigger: 'blur',
      });
      
      return rules;
    },

    // 名称验证
    name: (required = true, maxLength = 100) => {
      const rules: any[] = [];
      
      if (required) {
        rules.push({
          required: true,
          message: '请输入名称',
          trigger: 'blur',
        });
      }
      
      rules.push({
        max: maxLength,
        message: `名称长度不能超过${maxLength}个字符`,
        trigger: 'blur',
      });
      
      rules.push({
        validator: (_rule: any, value: string) => {
          if (!value && !required) return Promise.resolve();
          if (!ValidationUtils.validateName(value)) {
            return Promise.reject('名称格式不正确（不能包含特殊字符）');
          }
          return Promise.resolve();
        },
        trigger: 'blur',
      });
      
      return rules;
    },

    // 工期验证
    duration: (required = false) => {
      const rules: any[] = [];
      
      if (required) {
        rules.push({
          required: true,
          message: '请输入工期',
          trigger: 'blur',
        });
      }
      
      rules.push({
        validator: (_rule: any, value: number) => {
          if (!value && !required) return Promise.resolve();
          if (!ValidationUtils.validateDuration(value)) {
            return Promise.reject('工期必须为1-9999之间的正整数');
          }
          return Promise.resolve();
        },
        trigger: 'blur',
      });
      
      return rules;
    },

    // 描述验证
    description: (maxLength = 1000) => [
      {
        max: maxLength,
        message: `描述长度不能超过${maxLength}个字符`,
        trigger: 'blur',
      },
    ],

    // 邮箱验证
    email: (required = false) => {
      const rules: any[] = [];
      
      if (required) {
        rules.push({
          required: true,
          message: '请输入邮箱地址',
          trigger: 'blur',
        });
      }
      
      rules.push({
        type: 'email',
        message: '邮箱格式不正确',
        trigger: 'blur',
      });
      
      return rules;
    },

    // 手机号验证
    phone: (required = false) => {
      const rules: any[] = [];
      
      if (required) {
        rules.push({
          required: true,
          message: '请输入手机号',
          trigger: 'blur',
        });
      }
      
      rules.push({
        pattern: /^1[3-9]\d{9}$/,
        message: '手机号格式不正确',
        trigger: 'blur',
      });
      
      return rules;
    },

    // URL验证
    url: (required = false) => {
      const rules: any[] = [];
      
      if (required) {
        rules.push({
          required: true,
          message: '请输入URL地址',
          trigger: 'blur',
        });
      }
      
      rules.push({
        type: 'url',
        message: 'URL格式不正确',
        trigger: 'blur',
      });
      
      return rules;
    },

    // 数字范围验证
    numberRange: (min: number, max: number, required = false) => {
      const rules: any[] = [];
      
      if (required) {
        rules.push({
          required: true,
          message: '请输入数值',
          trigger: 'blur',
        });
      }
      
      rules.push({
        type: 'number',
        min,
        max,
        message: `数值必须在${min}-${max}之间`,
        trigger: 'blur',
      });
      
      return rules;
    },
  };

  /**
   * 异步验证方法
   */
  const asyncValidators = {
    // 检查名称是否重复
    checkNameExists: (checkFn: (name: string, excludeId?: string) => Promise<boolean>, excludeId?: string) => {
      return {
        validator: async (_rule: any, value: string) => {
          if (!value) return Promise.resolve();
          
          isValidating.value = true;
          try {
            const exists = await checkFn(value, excludeId);
            if (exists) {
              return Promise.reject('名称已存在');
            }
            return Promise.resolve();
          } finally {
            isValidating.value = false;
          }
        },
        trigger: 'blur',
      };
    },

    // 检查编码是否重复
    checkCodeExists: (checkFn: (code: string, excludeId?: string) => Promise<boolean>, excludeId?: string) => {
      return {
        validator: async (_rule: any, value: string) => {
          if (!value) return Promise.resolve();
          
          isValidating.value = true;
          try {
            const exists = await checkFn(value, excludeId);
            if (exists) {
              return Promise.reject('编码已存在');
            }
            return Promise.resolve();
          } finally {
            isValidating.value = false;
          }
        },
        trigger: 'blur',
      };
    },
  };

  /**
   * 模板特定的验证规则
   */
  const templateRules = {
    // 阶段模板验证
    phaseTemplate: {
      code: commonRules.code(true),
      name: commonRules.name(true, 100),
      description: commonRules.description(500),
      stdDuration: commonRules.duration(false),
    },

    // 阶段计划模板验证
    phasePlanTemplate: {
      name: commonRules.name(true, 255),
      description: commonRules.description(1000),
    },

    // 交付物库验证
    workProductLibrary: {
      code: commonRules.code(false), // 编码可以自动生成，所以不是必填
      name: commonRules.name(true, 255),
      description: commonRules.description(1000),
    },

    // 交付物计划模板验证
    workProductPlanTemplate: {
      name: commonRules.name(true, 255),
      description: commonRules.description(1000),
    },
  };

  /**
   * 验证单个字段
   */
  async function validateField(
    value: any,
    rules: any[],
    fieldName: string
  ): Promise<boolean> {
    try {
      for (const rule of rules) {
        if (rule.validator) {
          await rule.validator(rule, value);
        } else if (rule.required && (!value || value === '')) {
          throw new Error(rule.message || '此字段为必填项');
        } else if (rule.pattern && !rule.pattern.test(value)) {
          throw new Error(rule.message || '格式不正确');
        } else if (rule.max && value && value.length > rule.max) {
          throw new Error(rule.message || `长度不能超过${rule.max}个字符`);
        } else if (rule.min && value && value.length < rule.min) {
          throw new Error(rule.message || `长度不能少于${rule.min}个字符`);
        }
      }
      
      // 验证通过，清除错误
      delete validationErrors.value[fieldName];
      return true;
    } catch (error: any) {
      // 验证失败，记录错误
      validationErrors.value[fieldName] = error.message;
      return false;
    }
  }

  /**
   * 验证整个表单
   */
  async function validateForm(
    formData: Record<string, any>,
    rulesConfig: Record<string, any[]>
  ): Promise<boolean> {
    const validationPromises = Object.entries(rulesConfig).map(([fieldName, rules]) => {
      return validateField(formData[fieldName], rules, fieldName);
    });

    const results = await Promise.all(validationPromises);
    return results.every(Boolean);
  }

  /**
   * 清除验证错误
   */
  function clearValidationErrors(fieldName?: string) {
    if (fieldName) {
      delete validationErrors.value[fieldName];
    } else {
      validationErrors.value = {};
    }
  }

  /**
   * 获取字段错误信息
   */
  function getFieldError(fieldName: string): string | undefined {
    return validationErrors.value[fieldName];
  }

  /**
   * 检查是否有验证错误
   */
  const hasValidationErrors = computed(() => {
    return Object.keys(validationErrors.value).length > 0;
  });

  return {
    commonRules,
    asyncValidators,
    templateRules,
    validationErrors,
    isValidating,
    hasValidationErrors,
    validateField,
    validateForm,
    clearValidationErrors,
    getFieldError,
  };
}
