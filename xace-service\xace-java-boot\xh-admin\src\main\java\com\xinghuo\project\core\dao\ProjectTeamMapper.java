package com.xinghuo.project.core.dao;

import com.xinghuo.common.base.dao.XHBaseMapper;
import com.xinghuo.project.core.entity.ProjectTeamEntity;
import com.xinghuo.project.core.model.dto.ProjectTeamDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 项目团队Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Mapper
public interface ProjectTeamMapper extends XHBaseMapper<ProjectTeamEntity> {

    /**
     * 根据项目ID查询团队成员列表
     *
     * @param projectId 项目ID
     * @return 团队成员列表
     */
    List<ProjectTeamDTO> selectByProjectId(@Param("projectId") String projectId);

    /**
     * 根据用户ID查询参与的项目列表
     *
     * @param userId 用户ID
     * @return 项目ID列表
     */
    List<String> selectProjectIdsByUserId(@Param("userId") String userId);

    /**
     * 根据项目ID和用户ID查询团队成员信息
     *
     * @param projectId 项目ID
     * @param userId 用户ID
     * @return 团队成员信息
     */
    ProjectTeamEntity selectByProjectIdAndUserId(@Param("projectId") String projectId, @Param("userId") String userId);

    /**
     * 根据项目ID和角色ID查询团队成员列表
     *
     * @param projectId 项目ID
     * @param roleId 角色ID
     * @return 团队成员列表
     */
    List<ProjectTeamDTO> selectByProjectIdAndRoleId(@Param("projectId") String projectId, @Param("roleId") String roleId);

    /**
     * 查询项目经理列表
     *
     * @param projectId 项目ID
     * @return 项目经理列表
     */
    List<ProjectTeamDTO> selectManagersByProjectId(@Param("projectId") String projectId);

    /**
     * 查询主要负责人列表
     *
     * @param projectId 项目ID
     * @return 主要负责人列表
     */
    List<ProjectTeamDTO> selectMainResponsibleByProjectId(@Param("projectId") String projectId);

    /**
     * 批量插入团队成员
     *
     * @param teamMembers 团队成员列表
     * @return 插入数量
     */
    int batchInsert(@Param("teamMembers") List<ProjectTeamEntity> teamMembers);

    /**
     * 根据项目ID删除所有团队成员
     *
     * @param projectId 项目ID
     * @return 删除数量
     */
    int deleteByProjectId(@Param("projectId") String projectId);

    /**
     * 更新团队成员状态
     *
     * @param id 团队成员ID
     * @param teamStatus 团队状态
     * @return 更新数量
     */
    int updateTeamStatus(@Param("id") String id, @Param("teamStatus") Integer teamStatus);

    /**
     * 更新团队成员工作量
     *
     * @param id 团队成员ID
     * @param workloadPercent 工作量百分比
     * @return 更新数量
     */
    int updateWorkloadPercent(@Param("id") String id, @Param("workloadPercent") Double workloadPercent);

    /**
     * 检查用户是否为项目成员
     *
     * @param projectId 项目ID
     * @param userId 用户ID
     * @return 是否为项目成员
     */
    int checkUserInProject(@Param("projectId") String projectId, @Param("userId") String userId);

    /**
     * 检查用户是否为项目经理
     *
     * @param projectId 项目ID
     * @param userId 用户ID
     * @return 是否为项目经理
     */
    int checkUserIsManager(@Param("projectId") String projectId, @Param("userId") String userId);

    /**
     * 获取项目团队统计信息
     *
     * @param projectId 项目ID
     * @return 统计信息
     */
    java.util.Map<String, Object> getTeamStatistics(@Param("projectId") String projectId);

    /**
     * 根据部门ID查询项目团队成员
     *
     * @param departmentId 部门ID
     * @return 团队成员列表
     */
    List<ProjectTeamDTO> selectByDepartmentId(@Param("departmentId") String departmentId);

    /**
     * 查询用户在指定时间段内参与的项目
     *
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 项目ID列表
     */
    List<String> selectProjectIdsByUserIdAndTimeRange(@Param("userId") String userId, 
                                                      @Param("startTime") java.util.Date startTime, 
                                                      @Param("endTime") java.util.Date endTime);

    /**
     * 更新团队成员离开时间
     *
     * @param id 团队成员ID
     * @param leaveTime 离开时间
     * @return 更新数量
     */
    int updateLeaveTime(@Param("id") String id, @Param("leaveTime") java.util.Date leaveTime);

    /**
     * 查询活跃的团队成员
     *
     * @param projectId 项目ID
     * @return 活跃团队成员列表
     */
    List<ProjectTeamDTO> selectActiveTeamMembers(@Param("projectId") String projectId);

    /**
     * 根据角色名称查询团队成员
     *
     * @param projectId 项目ID
     * @param roleName 角色名称
     * @return 团队成员列表
     */
    List<ProjectTeamDTO> selectByProjectIdAndRoleName(@Param("projectId") String projectId, @Param("roleName") String roleName);
}
