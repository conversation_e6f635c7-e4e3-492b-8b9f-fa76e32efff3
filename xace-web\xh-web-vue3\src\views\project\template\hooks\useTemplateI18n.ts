import { computed } from 'vue';
import { useI18n } from '/@/hooks/web/useI18n';
import zhCN from '../locales/zh-CN';
import enUS from '../locales/en-US';

/**
 * 模板管理模块国际化Hook
 */
export function useTemplateI18n() {
  const { t, locale } = useI18n();

  // 模板模块的语言包
  const templateMessages = {
    'zh-CN': zhCN,
    'en-US': enUS,
  };

  /**
   * 获取当前语言的消息
   */
  const currentMessages = computed(() => {
    return templateMessages[locale.value] || templateMessages['zh-CN'];
  });

  /**
   * 模板专用的翻译函数
   */
  function tt(key: string, params?: Record<string, any>): string {
    const keys = key.split('.');
    let message: any = currentMessages.value;
    
    for (const k of keys) {
      if (message && typeof message === 'object' && k in message) {
        message = message[k];
      } else {
        // 如果找不到对应的翻译，返回key本身
        return key;
      }
    }
    
    if (typeof message !== 'string') {
      return key;
    }
    
    // 处理参数替换
    if (params) {
      return message.replace(/\{(\w+)\}/g, (match, paramKey) => {
        return params[paramKey] !== undefined ? String(params[paramKey]) : match;
      });
    }
    
    return message;
  }

  /**
   * 获取通用翻译
   */
  function getCommonText(key: string, params?: Record<string, any>): string {
    return tt(`common.${key}`, params);
  }

  /**
   * 获取阶段模板翻译
   */
  function getPhaseTemplateText(key: string, params?: Record<string, any>): string {
    return tt(`phaseTemplate.${key}`, params);
  }

  /**
   * 获取阶段计划模板翻译
   */
  function getPhasePlanTemplateText(key: string, params?: Record<string, any>): string {
    return tt(`phasePlanTemplate.${key}`, params);
  }

  /**
   * 获取交付物库翻译
   */
  function getWorkProductLibraryText(key: string, params?: Record<string, any>): string {
    return tt(`workProductLibrary.${key}`, params);
  }

  /**
   * 获取交付物计划模板翻译
   */
  function getWorkProductPlanTemplateText(key: string, params?: Record<string, any>): string {
    return tt(`workProductPlanTemplate.${key}`, params);
  }

  /**
   * 获取使用情况翻译
   */
  function getUsageInfoText(key: string, params?: Record<string, any>): string {
    return tt(`usageInfo.${key}`, params);
  }

  /**
   * 获取应用到项目翻译
   */
  function getApplyToProjectText(key: string, params?: Record<string, any>): string {
    return tt(`applyToProject.${key}`, params);
  }

  /**
   * 获取验证消息翻译
   */
  function getValidationText(key: string, params?: Record<string, any>): string {
    return tt(`validation.${key}`, params);
  }

  /**
   * 获取错误消息翻译
   */
  function getErrorText(key: string, params?: Record<string, any>): string {
    return tt(`errors.${key}`, params);
  }

  /**
   * 获取状态文本
   */
  function getStatusText(status: string | number): string {
    if (typeof status === 'number') {
      return status === 1 ? getCommonText('enabled') : getCommonText('disabled');
    }
    
    const statusMap: Record<string, string> = {
      'draft': getCommonText('draft'),
      'published': getCommonText('published'),
      'archived': getCommonText('archived'),
      'enabled': getCommonText('enabled'),
      'disabled': getCommonText('disabled'),
      'active': 'Active', // 可以添加到语言包中
      'inactive': 'Inactive',
    };
    
    return statusMap[status] || status;
  }

  /**
   * 获取布尔值文本
   */
  function getBooleanText(value: number | boolean): string {
    return (value === 1 || value === true) ? getCommonText('yes') : getCommonText('no');
  }

  /**
   * 格式化消息（支持参数）
   */
  function formatMessage(template: string, params: Record<string, any>): string {
    return template.replace(/\{(\w+)\}/g, (match, key) => {
      return params[key] !== undefined ? String(params[key]) : match;
    });
  }

  /**
   * 获取操作确认消息
   */
  function getConfirmMessage(action: string, itemName?: string, count?: number): string {
    if (count && count > 1) {
      return formatMessage(getCommonText('batchDeleteConfirm'), { count });
    } else if (itemName) {
      return formatMessage(getCommonText('deleteConfirm'), { name: itemName });
    } else {
      return `确定要${action}吗？`;
    }
  }

  /**
   * 获取成功消息
   */
  function getSuccessMessage(action: string, isBatch = false): string {
    const prefix = isBatch ? 'batch' : '';
    const key = `${prefix}${action}Success`;
    return getCommonText(key) || `${action}成功`;
  }

  /**
   * 获取字段标签
   */
  function getFieldLabel(module: string, field: string): string {
    return tt(`${module}.fields.${field}`) || field;
  }

  /**
   * 获取占位符文本
   */
  function getPlaceholder(module: string, field: string): string {
    return tt(`${module}.placeholders.${field}`) || `请输入${getFieldLabel(module, field)}`;
  }

  /**
   * 检查是否为中文环境
   */
  const isZhCN = computed(() => locale.value === 'zh-CN');

  /**
   * 检查是否为英文环境
   */
  const isEnUS = computed(() => locale.value === 'en-US');

  return {
    tt,
    getCommonText,
    getPhaseTemplateText,
    getPhasePlanTemplateText,
    getWorkProductLibraryText,
    getWorkProductPlanTemplateText,
    getUsageInfoText,
    getApplyToProjectText,
    getValidationText,
    getErrorText,
    getStatusText,
    getBooleanText,
    formatMessage,
    getConfirmMessage,
    getSuccessMessage,
    getFieldLabel,
    getPlaceholder,
    isZhCN,
    isEnUS,
    locale,
    currentMessages,
  };
}
