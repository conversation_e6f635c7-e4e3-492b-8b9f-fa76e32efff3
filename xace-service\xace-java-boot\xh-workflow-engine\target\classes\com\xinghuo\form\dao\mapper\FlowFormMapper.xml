<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinghuo.form.dao.FlowFormMapper">

    <resultMap id="FlowTempInfoModel" type="com.xinghuo.form.model.flow.FlowTempInfoModel">
        <id column="F_Id" property="id"/>
        <result column="F_EnCode" property="enCode"/>
    </resultMap>


    <select id="findFlowInfo" parameterType="String" resultMap="FlowTempInfoModel">
        SELECT ft.F_EnCode as F_EnCode,ft.F_Id as F_Id,F_EnabledMark as enabledMark FROM  flow_template ft where ft.F_Id =#{tempId}
    </select>

</mapper>
