package com.xinghuo.project.biz.service;

import com.xinghuo.common.base.service.BaseService;
import com.xinghuo.project.biz.entity.PaymentContractEntity;
import com.xinghuo.project.biz.model.PaymentContractPagination;

import java.util.Date;
import java.util.List;

/**
 * 付款合同服务接口
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
public interface PaymentContractService extends BaseService<PaymentContractEntity> {

    /**
     * 分页查询付款合同列表
     *
     * @param pagination 查询条件
     * @return 付款合同列表
     */
    List<PaymentContractEntity> getList(PaymentContractPagination pagination);

    /**
     * 根据收款合同ID查询付款合同列表
     *
     * @param contractId 收款合同ID
     * @return 付款合同列表
     */
    List<PaymentContractEntity> getListByContractId(String contractId);

    /**
     * 根据供应商ID查询付款合同列表
     *
     * @param supplierId 供应商ID
     * @return 付款合同列表
     */
    List<PaymentContractEntity> getListBySupplierId(String supplierId);

    /**
     * 根据ID查询付款合同信息
     *
     * @param id 付款合同ID
     * @return 付款合同信息
     */
    PaymentContractEntity getInfo(String id);

    /**
     * 创建付款合同
     *
     * @param entity 付款合同信息
     * @return 付款合同ID
     */
    String create(PaymentContractEntity entity);

    /**
     * 更新付款合同
     *
     * @param id 付款合同ID
     * @param entity 更新信息
     */
    void update(String id, PaymentContractEntity entity);

    /**
     * 删除付款合同
     *
     * @param id 付款合同ID
     */
    void delete(String id);

    /**
     * 更新付款合同状态
     *
     * @param id 付款合同ID
     * @param status 状态
     */
    void updateStatus(String id, String status);

    /**
     * 签订付款合同
     *
     * @param id 付款合同ID
     * @param cNo 合同编号
     * @param signDate 签订日期
     */
    void sign(String id, String cNo, Date signDate);

    /**
     * 检查付款合同编号是否存在
     *
     * @param cNo 合同编号
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean isExistByCNo(String cNo, String excludeId);

    /**
     * 获取付款合同选择列表
     *
     * @param keyword 关键字
     * @return 付款合同列表
     */
    List<PaymentContractEntity> getSelectList(String keyword);
}
