package com.xinghuo.project.core.model.dto;

import com.xinghuo.project.core.entity.ProjectTeamEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 项目团队DTO
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProjectTeamDTO extends ProjectTeamEntity {

    /**
     * 用户名
     */
    private String userName;

    /**
     * 用户显示名称
     */
    private String displayName;

    /**
     * 用户类型
     * 0-用户, 1-组织
     */
    private Integer userType;

    /**
     * 部门ID
     */
    private String departmentId;

    /**
     * 部门名称
     */
    private String departmentName;

    /**
     * 部门编码
     */
    private String departmentCode;

    /**
     * 是否删除状态
     */
    private Boolean deleteStatus;

    /**
     * 是否公司管理员
     */
    private Boolean companyAdmin;

    /**
     * 是否用户类型
     */
    private Boolean isUser;

    /**
     * 是否角色类型
     */
    private Boolean isRole;

    /**
     * 角色用户列表
     */
    private List<ProjectTeamDTO> userList;

    /**
     * 子成员列表
     */
    private List<ProjectTeamDTO> children;

    /**
     * 是否关闭
     */
    private Boolean close;

    /**
     * 是否必需
     */
    private Boolean required;

    /**
     * 头像URL
     */
    private String avatarUrl;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 职位
     */
    private String position;

    /**
     * 技能标签
     */
    private List<String> skills;

    /**
     * 工作经验年限
     */
    private Integer workExperience;

    /**
     * 成本单价
     */
    private Double costRate;

    /**
     * 可用性状态
     * 1-可用, 2-忙碌, 3-不可用
     */
    private Integer availabilityStatus;

    /**
     * 项目角色权限
     */
    private List<String> rolePermissions;

    /**
     * 团队绩效评分
     */
    private Double performanceScore;

    /**
     * 加入项目原因
     */
    private String joinReason;

    /**
     * 离开项目原因
     */
    private String leaveReason;

    /**
     * 团队协作评价
     */
    private String collaborationComment;

    /**
     * 是否核心成员
     */
    private Boolean isCoreTeamMember;

    /**
     * 汇报关系
     */
    private String reportingRelation;

    /**
     * 项目贡献度
     */
    private Double contributionRate;

    /**
     * 扩展属性
     */
    private java.util.Map<String, Object> extendProperties;
}
