var MAGIC_EDITOR_CONFIG={title:"\u5feb\u901f\u5f00\u53d1\u5e73\u53f0",header:{skin:false,document:false,repo:false,qqGroup:false},request:{beforeSend:function(config){var token=getQueryString("Authorization");if(token)config.headers.Authorization=token;return config},onError:function(err){console.log("\u8bf7\u6c42\u51fa\u9519");return Promise.reject(err)}},getMagicTokenValue:function(){return getQueryString("Authorization")},response:{onSuccess:function(resp){return resp},onError:function(err){console.log("\u8bf7\u6c42\u51fa\u9519");
            return Promise.reject(err)}}};function getQueryString(name){var reg=new RegExp("(^|&)"+name+"=([^&]*)(&|$)","i");var r=window.location.search.substr(1).match(reg);var context="";if(r!=null)context=decodeURIComponent(r[2]);reg=null;r=null;return context==null||context==""||context=="undefined"?"":context};
