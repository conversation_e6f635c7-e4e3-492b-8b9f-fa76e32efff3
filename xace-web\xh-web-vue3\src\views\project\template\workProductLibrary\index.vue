<template>
  <div>
    <BasicTable @register="registerTable" :searchInfo="searchInfo">
      <template #toolbar>
        <a-button type="primary" @click="handleCreate">
          <Icon icon="ant-design:plus-outlined" /> 新建交付物
        </a-button>
        <a-button @click="handleBatchDelete" :disabled="!selectedRowKeys.length">
          <Icon icon="ant-design:delete-outlined" /> 批量删除
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'clarity:note-edit-line',
                tooltip: '编辑',
                onClick: handleEdit.bind(null, record),
              },
              {
                icon: 'ant-design:eye-outlined',
                tooltip: '查看详情',
                onClick: handleView.bind(null, record),
              },
              {
                icon: 'ant-design:copy-outlined',
                tooltip: '复制',
                onClick: handleCopy.bind(null, record),
              },
              {
                icon: 'ant-design:delete-outlined',
                tooltip: '删除',
                color: 'error',
                popConfirm: {
                  title: '是否确认删除',
                  placement: 'left',
                  confirm: handleDelete.bind(null, record),
                },
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <WorkProductLibraryModal @register="registerModal" @success="handleSuccess" />
    <WorkProductLibraryCopyModal @register="registerCopyModal" @success="handleSuccess" />
    <WorkProductLibraryDetailModal @register="registerDetailModal" />
  </div>
</template>

<script lang="ts" setup>
  import { reactive, ref } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { useModal } from '/@/components/Modal';
  import { Icon } from '/@/components/Icon';
  import { useMessage } from '/@/hooks/web/useMessage';
  
  import WorkProductLibraryModal from './WorkProductLibraryModal.vue';
  import WorkProductLibraryCopyModal from './WorkProductLibraryCopyModal.vue';
  import WorkProductLibraryDetailModal from './WorkProductLibraryDetailModal.vue';
  import { columns, searchFormSchema } from './workProductLibrary.data';
  import {
    getWorkProductLibraryList,
    deleteWorkProductLibrary,
    batchDeleteWorkProductLibrary,
  } from '/@/api/project/workProductLibrary';

  defineOptions({ name: 'WorkProductLibrary' });

  const { createMessage } = useMessage();
  const [registerModal, { openModal }] = useModal();
  const [registerCopyModal, { openModal: openCopyModal }] = useModal();
  const [registerDetailModal, { openModal: openDetailModal }] = useModal();
  const searchInfo = reactive<Recordable>({});
  const selectedRowKeys = ref<string[]>([]);

  const [registerTable, { reload, getSelectRowKeys }] = useTable({
    title: '标准交付物库列表',
    api: getWorkProductLibraryList,
    rowKey: 'id',
    columns,
    formConfig: {
      labelWidth: 120,
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
    },
    useSearchForm: true,
    showTableSetting: true,
    bordered: true,
    handleSearchInfoFn(info) {
      console.log('handleSearchInfoFn', info);
      return info;
    },
    actionColumn: {
      width: 120,
      title: '操作',
      dataIndex: 'action',
      // slots: { customRender: 'action' },
    },
    rowSelection: {
      type: 'checkbox',
      onChange: (keys: string[]) => {
        selectedRowKeys.value = keys;
      },
    },
  });

  function handleCreate() {
    openModal(true, {
      isUpdate: false,
    });
  }

  function handleEdit(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
    });
  }

  function handleView(record: Recordable) {
    openDetailModal(true, {
      record,
    });
  }

  function handleCopy(record: Recordable) {
    openCopyModal(true, {
      record,
    });
  }

  async function handleDelete(record: Recordable) {
    try {
      await deleteWorkProductLibrary(record.id);
      createMessage.success('删除成功');
      reload();
    } catch (error) {
      console.error('删除失败:', error);
    }
  }

  async function handleBatchDelete() {
    try {
      const keys = getSelectRowKeys();
      if (keys.length === 0) {
        createMessage.warning('请选择要删除的数据');
        return;
      }
      await batchDeleteWorkProductLibrary(keys);
      createMessage.success('批量删除成功');
      selectedRowKeys.value = [];
      reload();
    } catch (error) {
      console.error('批量删除失败:', error);
    }
  }

  function handleSuccess({ isUpdate, values }) {
    if (isUpdate) {
      // 演示不刷新表格直接更新内部数据。
      // 注意：updateTableDataRecord要求表格的rowKey属性为string并且存在于每一行的record的keys中
      const result = updateTableDataRecord(values.id, values);
      console.log(result);
    } else {
      reload();
    }
  }
</script>
