package com.xinghuo.project.core.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 项目团队实体
 * 对应表：zz_proj_team
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("zz_proj_team")
public class ProjectTeamEntity extends BaseEntityV2.CUBaseEntityV2<String> {

    /**
     * 项目ID
     */
    @TableField("project_id")
    private String projectId;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 角色ID
     */
    @TableField("role_id")
    private String roleId;

    /**
     * 角色名称
     */
    @TableField("role_name")
    private String roleName;

    /**
     * 是否主要负责人
     */
    @TableField("is_main_responsible")
    private Boolean isMainResponsible;

    /**
     * 加入时间
     */
    @TableField("join_time")
    private Date joinTime;

    /**
     * 离开时间
     */
    @TableField("leave_time")
    private Date leaveTime;

    /**
     * 团队状态
     * 1-活跃, 2-暂停, 3-离开
     */
    @TableField("team_status")
    private Integer teamStatus;

    /**
     * 工作量分配百分比
     */
    @TableField("workload_percent")
    private Double workloadPercent;

    /**
     * 职责描述
     */
    @TableField("duty_desc")
    private String dutyDesc;

    /**
     * 联系方式
     */
    @TableField("contact_info")
    private String contactInfo;

    /**
     * 备注
     */
    @TableField("remarks")
    private String remarks;
}
