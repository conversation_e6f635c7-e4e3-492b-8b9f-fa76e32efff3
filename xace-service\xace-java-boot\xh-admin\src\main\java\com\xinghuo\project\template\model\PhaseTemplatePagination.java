package com.xinghuo.project.template.model;

import com.xinghuo.common.base.model.Pagination;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 阶段模板分页查询参数
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PhaseTemplatePagination extends Pagination {

    /**
     * 阶段编码
     */
    private String code;

    /**
     * 阶段名称
     */
    private String name;

    /**
     * 状态 (1:启用, 0:禁用)
     */
    private Integer status;

    /**
     * 关键字搜索（编码或名称）
     */
    private String keyword;

    /**
     * 创建时间开始
     */
    private Date createTimeStart;

    /**
     * 创建时间结束
     */
    private Date createTimeEnd;

    /**
     * 标准工期最小值
     */
    private Integer stdDurationMin;

    /**
     * 标准工期最大值
     */
    private Integer stdDurationMax;

    /**
     * 创建用户ID
     */
    private String creatorUserId;
}
