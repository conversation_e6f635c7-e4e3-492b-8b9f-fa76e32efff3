<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xxl.job.admin.dao.XxlJobUserMapper">

    <resultMap id="XxlJobUser" type="com.xxl.job.admin.core.model.XxlJobUser">
        <result column="id" property="id"/>
        <result column="username" property="username"/>
        <result column="password" property="password"/>
        <result column="role" property="role"/>
        <result column="permission" property="permission"/>
    </resultMap>

    <sql id="Base_Column_List">
		t.id,
		t.username,
		t.password,
		t.role,
		t.permission
	</sql>

</mapper>
