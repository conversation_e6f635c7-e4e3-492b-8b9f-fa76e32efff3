package com.xinghuo.project.biz.controller;


import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.constant.MsgCode;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.permission.entity.OrganizeEntity;
import com.xinghuo.permission.entity.UserEntity;
import com.xinghuo.permission.service.OrganizeService;
import com.xinghuo.permission.service.UserService;
import com.xinghuo.project.biz.entity.BizContractEntity;
import com.xinghuo.project.biz.model.bizContract.BizContractDateUpdateForm;
import com.xinghuo.project.biz.model.bizContract.BizContractForm;
import com.xinghuo.project.biz.model.bizContract.BizContractPagination;
import com.xinghuo.project.biz.model.bizContract.BizContractVO;
import com.xinghuo.project.biz.service.BizContractService;
import com.xinghuo.project.biz.service.BizCustomerService;
import com.xinghuo.system.base.service.DictionaryDataService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 合同管理
 */
@Slf4j
@RestController
@Tag(name = "合同管理", description = "BizContract")
@RequestMapping("/api/project/biz/contract")
public class BizContractController {

    @Autowired
    private BizContractService contractService;

    @Autowired
    private BizCustomerService customerService;

    @Autowired
    private UserService userService;

    @Autowired
    private OrganizeService organizeService;

    @Autowired
    private DictionaryDataService dictionaryDataService;

    /**
     * 获取合同列表
     *
     * @param pagination 分页查询参数
     * @return 合同列表
     */
    @PostMapping("/getList")
    @Operation(summary = "获取合同列表")
    public ActionResult list(@RequestBody BizContractPagination pagination) {
        List<BizContractEntity> list = contractService.getList(pagination);
        List<BizContractVO> listVOs = BeanCopierUtils.copyList(list, BizContractVO.class);

        // 处理字段转换
        for (BizContractVO vo : listVOs) {
            // 处理客户单位名称
            if (vo.getCustId() != null && !vo.getCustId().isEmpty()) {
                try {
                    var customer = customerService.getInfo(vo.getCustId());
                    if (customer != null) {
                        vo.setCustName(customer.getName());
                    }
                } catch (Exception e) {
                    log.warn("获取客户单位名称失败，客户ID: {}", vo.getCustId(), e);
                }
            }

            // 处理最终用户名称
            if (vo.getFinalUserId() != null && !vo.getFinalUserId().isEmpty()) {
                try {
                    var finalUser = customerService.getInfo(vo.getFinalUserId());
                    if (finalUser != null) {
                        vo.setFinalUserName(finalUser.getName());
                    }
                } catch (Exception e) {
                    log.warn("获取最终用户名称失败，用户ID: {}", vo.getFinalUserId(), e);
                }
            }

            // 处理项目经理名称
            if (vo.getOwnId() != null && !vo.getOwnId().isEmpty()) {
                try {
                    UserEntity user = userService.getInfo(vo.getOwnId());
                    if (user != null) {
                        vo.setOwnName(user.getRealName());
                    }
                } catch (Exception e) {
                    log.warn("获取项目经理名称失败，用户ID: {}", vo.getOwnId(), e);
                }
            }

            // 处理所属部门名称
            if (vo.getDeptId() != null && !vo.getDeptId().isEmpty()) {
                try {
                    OrganizeEntity dept = organizeService.getInfo(vo.getDeptId());
                    if (dept != null) {
                        vo.setDeptName(dept.getFullName());
                    }
                } catch (Exception e) {
                    log.warn("获取部门名称失败，部门ID: {}", vo.getDeptId(), e);
                }
            }

            // 处理续签状态文本转换
            if (vo.getIsContinue() != null) {
                switch (vo.getIsContinue()) {
                    case 0:
                        vo.setIsContinueText("正常");
                        break;
                    case 1:
                        vo.setIsContinueText("已续签");
                        break;
                    case 9:
                        vo.setIsContinueText("不续签");
                        break;
                    default:
                        vo.setIsContinueText("未知");
                        break;
                }
            }

            // 注释：数据字典翻译移到前端处理，提高性能和缓存效率

            // 处理是否外采状态文本转换
            if (vo.getExternalStatus() != null) {
                switch (vo.getExternalStatus()) {
                    case "0":
                        vo.setExternalStatusText("否");
                        break;
                    case "1":
                        vo.setExternalStatusText("是");
                        break;
                    default:
                        vo.setExternalStatusText(vo.getExternalStatus());
                        break;
                }
            }
        }

        PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
        return ActionResult.page(listVOs, page);
    }


    /**
     * 根据客户ID获取合同列表
     *
     * @param customerId 客户ID
     * @return 合同列表
     */
    @GetMapping("/customer/{customerId}")
    @Operation(summary = "根据客户ID获取合同列表")
    @Parameters({
            @Parameter(name = "customerId", description = "客户ID", required = true),
    })
    public ActionResult<List<BizContractVO>> listByCustomerId(@PathVariable("customerId") String customerId) {
        List<BizContractEntity> list = contractService.getListByCustomerId(customerId);
        List<BizContractVO> listVOs = BeanCopierUtils.copyList(list, BizContractVO.class);
        return ActionResult.success(listVOs);
    }

    /**
     * 获取合同详情
     *
     * @param id 合同ID
     * @return 合同详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取合同详情")
    @Parameters({
            @Parameter(name = "id", description = "合同ID", required = true),
    })
    public ActionResult<BizContractVO> info(@PathVariable("id") String id) {
        BizContractEntity entity = contractService.getInfo(id);
        BizContractVO vo = BeanCopierUtils.copy(entity, BizContractVO.class);

        // 处理字段转换（与list方法保持一致）
        // 处理客户单位名称
        if (vo.getCustId() != null && !vo.getCustId().isEmpty()) {
            try {
                var customer = customerService.getInfo(vo.getCustId());
                if (customer != null) {
                    vo.setCustName(customer.getName());
                }
            } catch (Exception e) {
                log.warn("获取客户单位名称失败，客户ID: {}", vo.getCustId(), e);
            }
        }

        // 处理最终用户名称
        if (vo.getFinalUserId() != null && !vo.getFinalUserId().isEmpty()) {
            try {
                var finalUser = customerService.getInfo(vo.getFinalUserId());
                if (finalUser != null) {
                    vo.setFinalUserName(finalUser.getName());
                }
            } catch (Exception e) {
                log.warn("获取最终用户名称失败，用户ID: {}", vo.getFinalUserId(), e);
            }
        }

        // 处理项目经理名称
        if (vo.getOwnId() != null && !vo.getOwnId().isEmpty()) {
            try {
                UserEntity user = userService.getInfo(vo.getOwnId());
                if (user != null) {
                    vo.setOwnName(user.getRealName());
                }
            } catch (Exception e) {
                log.warn("获取项目经理名称失败，用户ID: {}", vo.getOwnId(), e);
            }
        }

        // 处理所属部门名称
        if (vo.getDeptId() != null && !vo.getDeptId().isEmpty()) {
            try {
                OrganizeEntity dept = organizeService.getInfo(vo.getDeptId());
                if (dept != null) {
                    vo.setDeptName(dept.getFullName());
                }
            } catch (Exception e) {
                log.warn("获取部门名称失败，部门ID: {}", vo.getDeptId(), e);
            }
        }

        // 注释：数据字典翻译移到前端处理，提高性能和缓存效率

        return ActionResult.success(vo);
    }

    /**
     * 创建合同
     *
     * @param contractForm 合同表单
     * @return 操作结果
     */
    @PostMapping
    @Operation(summary = "创建合同")
    @Parameters({
            @Parameter(name = "contractForm", description = "合同表单", required = true),
    })
    public ActionResult create(@RequestBody @Valid BizContractForm contractForm) {
        // 检查合同编号是否已存在
        if (contractService.isExistByCNo(contractForm.getCno(), null)) {
            return ActionResult.fail("合同编号已存在");
        }

        BizContractEntity entity = BeanCopierUtils.copy(contractForm, BizContractEntity.class);
        contractService.create(entity);
        return ActionResult.success(MsgCode.SU001.get());
    }

    /**
     * 更新合同
     *
     * @param id           合同ID
     * @param contractForm 合同表单
     * @return 操作结果
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新合同")
    @Parameters({
            @Parameter(name = "id", description = "合同ID", required = true),
            @Parameter(name = "contractForm", description = "合同表单", required = true),
    })
    public ActionResult update(@PathVariable("id") String id, @RequestBody @Valid BizContractForm contractForm) {
        // 检查合同编号是否已存在
        if (contractService.isExistByCNo(contractForm.getCno(), id)) {
            return ActionResult.fail("合同编号已存在");
        }

        BizContractEntity entity = BeanCopierUtils.copy(contractForm, BizContractEntity.class);
        contractService.update(id, entity);
        return ActionResult.success(MsgCode.SU002.get());
    }

    /**
     * 删除合同
     *
     * @param id 合同ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除合同")
    @Parameters({
            @Parameter(name = "id", description = "合同ID", required = true),
    })
    public ActionResult delete(@PathVariable("id") String id) {
        contractService.delete(id);
        return ActionResult.success(MsgCode.SU003.get());
    }

    /**
     * 更新合同状态
     *
     * @param id     合同ID
     * @param status 合同状态
     * @return 操作结果
     */
    @PutMapping("/{id}/status/{status}")
    @Operation(summary = "更新合同状态")
    @Parameters({
            @Parameter(name = "id", description = "合同ID", required = true),
            @Parameter(name = "status", description = "合同状态", required = true),
    })
    public ActionResult updateStatus(@PathVariable("id") String id, @PathVariable("status") String status) {
        contractService.updateStatus(id, status);
        return ActionResult.success(MsgCode.SU002.get());
    }

    /**
     * 更新合同日期
     *
     * @param id       合同ID
     * @param dateForm 日期更新表单
     * @return 操作结果
     */
    @PutMapping("/{id}/date")
    @Operation(summary = "更新合同日期")
    @Parameters({
            @Parameter(name = "id", description = "合同ID", required = true),
            @Parameter(name = "dateForm", description = "日期更新表单", required = true),
    })
    public ActionResult updateDate(@PathVariable("id") String id,
                                   @RequestBody @Valid BizContractDateUpdateForm dateForm) {
        contractService.updateDate(id, dateForm.getDateType(), dateForm.getNewDate(), dateForm.getNote());
        return ActionResult.success(MsgCode.SU002.get());
    }

    /**
     * 获取合同选择器列表
     *
     * @param keyword 关键字搜索
     * @return 合同选择器列表
     */
    @GetMapping("/selector")
    @Operation(summary = "获取合同选择器列表")
    public ActionResult<?> selector(@RequestParam(value = "keyword", required = false) String keyword) {
        BizContractPagination pagination = new BizContractPagination();
        pagination.setKeyword(keyword);
        pagination.setPageSize(3000); // 限制返回数量

        List<BizContractEntity> list = contractService.getList(pagination);
        List<Map<String, Object>> selectorList = new ArrayList<>();

        for (BizContractEntity contract : list) {
            Map<String, Object> option = new HashMap<>();
            option.put("id", contract.getId());
            option.put("fullName", contract.getCno() + " - " + contract.getName());
            option.put("cNo", contract.getCno());
            option.put("name", contract.getName());
            selectorList.add(option);
        }

        return ActionResult.success(selectorList);
    }
}

