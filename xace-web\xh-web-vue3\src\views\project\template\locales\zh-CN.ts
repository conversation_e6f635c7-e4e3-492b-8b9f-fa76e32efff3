/**
 * 项目模板管理模块 - 中文语言包
 */
export default {
  // 通用
  common: {
    create: '新建',
    edit: '编辑',
    delete: '删除',
    copy: '复制',
    view: '查看',
    detail: '详情',
    search: '搜索',
    reset: '重置',
    confirm: '确认',
    cancel: '取消',
    save: '保存',
    submit: '提交',
    back: '返回',
    close: '关闭',
    refresh: '刷新',
    export: '导出',
    import: '导入',
    batchDelete: '批量删除',
    batchEnable: '批量启用',
    batchDisable: '批量禁用',
    batchPublish: '批量发布',
    batchArchive: '批量归档',
    selectAll: '全选',
    selectNone: '取消全选',
    operation: '操作',
    status: '状态',
    createTime: '创建时间',
    updateTime: '更新时间',
    creator: '创建人',
    updater: '更新人',
    remark: '备注',
    description: '描述',
    name: '名称',
    code: '编码',
    type: '类型',
    enabled: '启用',
    disabled: '禁用',
    published: '已发布',
    draft: '未发布',
    archived: '已归档',
    yes: '是',
    no: '否',
    loading: '加载中...',
    noData: '暂无数据',
    total: '共 {count} 条',
  },

  // 阶段模板
  phaseTemplate: {
    title: '阶段模板',
    list: '阶段模板列表',
    create: '新建阶段模板',
    edit: '编辑阶段模板',
    copy: '复制阶段模板',
    detail: '阶段模板详情',
    fields: {
      code: '阶段编码',
      name: '阶段名称',
      description: '阶段描述',
      stdDuration: '标准工期',
      status: '状态',
      defaultApproval: '默认审批流程',
      defaultChecklist: '默认检查单模板',
      usageCount: '使用次数',
      projectTemplateCount: '关联项目模板数量',
    },
    placeholders: {
      code: '请输入阶段编码',
      name: '请输入阶段名称',
      description: '请输入阶段描述',
      stdDuration: '请输入标准工期（天）',
      searchKeyword: '请输入阶段名称或编码',
    },
    messages: {
      createSuccess: '阶段模板创建成功',
      updateSuccess: '阶段模板更新成功',
      deleteSuccess: '阶段模板删除成功',
      copySuccess: '阶段模板复制成功',
      enableSuccess: '阶段模板启用成功',
      disableSuccess: '阶段模板禁用成功',
      batchDeleteSuccess: '批量删除成功',
      batchEnableSuccess: '批量启用成功',
      batchDisableSuccess: '批量禁用成功',
      deleteConfirm: '确定要删除阶段模板"{name}"吗？',
      batchDeleteConfirm: '确定要删除选中的 {count} 个阶段模板吗？',
      selectRecords: '请选择要操作的记录',
    },
  },

  // 阶段计划模板
  phasePlanTemplate: {
    title: '阶段计划模板',
    list: '阶段计划模板列表',
    create: '新建阶段计划模板',
    edit: '编辑阶段计划模板',
    copy: '复制阶段计划模板',
    detail: '阶段计划模板详情',
    fields: {
      name: '模板名称',
      description: '模板描述',
      knStatus: '知识状态',
      phaseCount: '阶段总数',
      totalDuration: '总工期',
      projectTemplateCount: '关联项目模板数',
      usageCount: '使用次数',
      phases: '阶段明细',
      relatedProjects: '关联项目模板',
    },
    placeholders: {
      name: '请输入模板名称',
      description: '请输入模板描述',
      searchKeyword: '请输入模板名称',
    },
    messages: {
      createSuccess: '阶段计划模板创建成功',
      updateSuccess: '阶段计划模板更新成功',
      deleteSuccess: '阶段计划模板删除成功',
      copySuccess: '阶段计划模板复制成功',
      publishSuccess: '阶段计划模板发布成功',
      archiveSuccess: '阶段计划模板归档成功',
      applySuccess: '模板应用成功',
    },
  },

  // 标准交付物库
  workProductLibrary: {
    title: '标准交付物库',
    list: '标准交付物库列表',
    create: '新建交付物',
    edit: '编辑交付物',
    copy: '复制交付物',
    detail: '交付物详情',
    fields: {
      code: '交付物编码',
      name: '交付物名称',
      description: '交付物描述',
      type: '交付物类型',
      subType: '交付物子类型',
      defaultRole: '默认责任角色',
      needReview: '需要评审',
      isDeliverable: '最终交付成果',
      canCut: '可裁剪',
      status: '状态',
      usageCount: '使用次数',
      projectTemplateCount: '关联项目模板数量',
    },
    placeholders: {
      code: '请输入交付物编码（可自动生成）',
      name: '请输入交付物名称',
      description: '请输入交付物描述',
      searchKeyword: '请输入交付物名称或编码',
    },
    messages: {
      createSuccess: '交付物创建成功',
      updateSuccess: '交付物更新成功',
      deleteSuccess: '交付物删除成功',
      copySuccess: '交付物复制成功',
      codeGenerated: '编码已自动生成',
    },
  },

  // 交付物计划模板
  workProductPlanTemplate: {
    title: '交付物计划模板',
    list: '交付物计划模板列表',
    create: '新建交付物计划模板',
    edit: '编辑交付物计划模板',
    copy: '复制交付物计划模板',
    detail: '交付物计划模板详情',
    fields: {
      name: '模板名称',
      description: '模板描述',
      knStatus: '知识状态',
      workProductCount: '交付物总数',
      reviewRequiredCount: '需评审数量',
      deliverableCount: '最终交付成果数量',
      projectTemplateCount: '关联项目模板数',
      workProducts: '交付物明细',
    },
    placeholders: {
      name: '请输入模板名称',
      description: '请输入模板描述',
      searchKeyword: '请输入模板名称',
    },
    messages: {
      createSuccess: '交付物计划模板创建成功',
      updateSuccess: '交付物计划模板更新成功',
      deleteSuccess: '交付物计划模板删除成功',
      copySuccess: '交付物计划模板复制成功',
      publishSuccess: '交付物计划模板发布成功',
      archiveSuccess: '交付物计划模板归档成功',
    },
  },

  // 使用情况统计
  usageInfo: {
    title: '使用情况统计',
    basicStats: '基本统计',
    usageTrend: '使用趋势',
    relatedProjects: '关联项目列表',
    recentUsage: '最近使用记录',
    fields: {
      totalUsage: '总使用次数',
      projectCount: '关联项目数',
      activeProjectCount: '活跃项目数',
      period: '时间',
      count: '使用次数',
      newProjects: '新增项目数',
      activeProjects: '活跃项目数',
      projectName: '项目名称',
      projectCode: '项目编码',
      projectStatus: '项目状态',
      usedAt: '使用时间',
      usedBy: '使用人',
      actionType: '操作类型',
      actionTime: '操作时间',
      actionBy: '操作人',
    },
    messages: {
      noUsageData: '暂无使用记录',
    },
  },

  // 应用到项目
  applyToProject: {
    title: '应用模板到项目',
    templateInfo: '模板信息',
    projectSelection: '选择目标项目',
    applyOptions: '应用选项',
    preview: '应用预览',
    fields: {
      templateName: '模板名称',
      templateType: '模板类型',
      targetProject: '目标项目',
      overwrite: '覆盖现有配置',
      backup: '应用前备份现有配置',
      notify: '应用完成后通知项目成员',
      remark: '应用说明',
      phaseCount: '将添加阶段数',
      activityCount: '将添加活动数',
      workProductCount: '将添加交付物数',
      totalDuration: '预计总工期',
    },
    placeholders: {
      targetProject: '请选择要应用模板的项目',
      remark: '请输入应用说明（可选）',
    },
    messages: {
      applySuccess: '模板应用成功',
      selectProject: '请选择目标项目',
      previewInfo: '将向项目 "{projectName}" 应用模板 "{templateName}"',
    },
  },

  // 验证消息
  validation: {
    required: '此字段为必填项',
    codeFormat: '编码格式不正确（字母开头，可包含字母、数字、下划线，长度3-20）',
    nameFormat: '名称格式不正确（不能包含特殊字符）',
    durationRange: '工期必须为1-9999之间的正整数',
    maxLength: '长度不能超过{max}个字符',
    minLength: '长度不能少于{min}个字符',
    emailFormat: '邮箱格式不正确',
    phoneFormat: '手机号格式不正确',
    urlFormat: 'URL格式不正确',
    numberRange: '数值必须在{min}-{max}之间',
    nameExists: '名称已存在',
    codeExists: '编码已存在',
  },

  // 错误消息
  errors: {
    networkError: '网络连接失败，请检查网络设置',
    serverError: '服务器错误，请稍后重试',
    permissionDenied: '权限不足，无法执行此操作',
    dataNotFound: '数据不存在或已被删除',
    operationFailed: '操作失败，请重试',
    validationFailed: '数据验证失败',
    timeout: '操作超时，请重试',
  },
};
