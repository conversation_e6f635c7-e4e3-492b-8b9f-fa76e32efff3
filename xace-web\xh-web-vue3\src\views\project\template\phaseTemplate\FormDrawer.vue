<template>
  <BasicDrawer
    v-bind="$attrs"
    @register="registerDrawer"
    :title="getTitle"
    width="70%"
    showFooter
    :canFullscreen="true"
    :maskClosable="false"
    :keyboard="false"
    class="phase-template-form-drawer"
    @ok="handleSubmit"
  >
    <template #footer>
      <a-space>
        <a-button @click="closeDrawer">
          <template #icon><CloseOutlined /></template>
          取消
        </a-button>
        <a-button type="primary" @click="handleSubmit" :loading="loading">
          <template #icon><SaveOutlined /></template>
          {{ isUpdate ? '更新' : '保存' }}
        </a-button>
      </a-space>
    </template>

    <div class="phase-template-form-container">
      <BasicForm @register="registerForm">
        <template #description="{ model, field }">
          <a-textarea
            v-model:value="model[field]"
            placeholder="请输入阶段描述"
            :rows="4"
            :maxlength="1000"
            show-count
          />
        </template>
      </BasicForm>
    </div>
  </BasicDrawer>
</template>

<script lang="ts" setup>
  import { computed, ref, unref, nextTick } from 'vue';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { BasicForm, useForm } from '/@/components/Form';
  import { FormSchema } from '/@/components/Form/src/types/form';
  import { createPhaseTemplate, updatePhaseTemplate, getPhaseTemplateInfo } from '/@/api/project/phaseTemplate';
  import { useMessage } from '/@/hooks/web/useMessage';
  import {
    SaveOutlined,
    CloseOutlined,
  } from '@ant-design/icons-vue';

  const { createMessage } = useMessage();
  const emit = defineEmits(['register', 'reload']);
  const isUpdate = ref(false);
  const phaseTemplateId = ref('');
  const phaseTemplateInfo = ref<any>({});
  const loading = ref(false);

  // 表单配置
  const formSchemas: FormSchema[] = [
    {
      field: 'code',
      label: '阶段编码',
      component: 'Input',
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请输入阶段编码（可自动生成）',
        size: 'large',
      },
      rules: [
        {
          pattern: /^[A-Z]{2}\d{8}$/,
          message: '阶段编码格式为：PL + 8位数字',
          trigger: 'blur',
        },
      ],
    },
    {
      field: 'name',
      label: '阶段名称',
      component: 'Input',
      required: true,
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请输入阶段名称',
        size: 'large',
        maxlength: 255,
      },
      rules: [{ required: true, trigger: 'blur', message: '请输入阶段名称' }],
    },
    {
      field: 'stdDuration',
      label: '标准工期(天)',
      component: 'InputNumber',
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请输入标准工期',
        size: 'large',
        style: { width: '100%' },
        min: 1,
        max: 9999,
      },
    },
    {
      field: 'status',
      label: '状态',
      component: 'RadioButtonGroup',
      defaultValue: 1,
      colProps: { span: 12 },
      componentProps: {
        options: [
          { label: '启用', value: 1 },
          { label: '禁用', value: 0 },
        ],
      },
    },
    {
      field: 'defaultApprovalId',
      label: '默认审批流程',
      component: 'Select',
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请选择默认审批流程',
        options: [],
      },
    },
    {
      field: 'defaultChecklistId',
      label: '默认检查单',
      component: 'Select',
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请选择默认检查单',
        options: [],
      },
    },
    {
      field: 'description',
      label: '阶段描述',
      component: 'Input',
      colProps: { span: 24 },
      slot: 'description',
    },
  ];

  // 注册表单
  const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 120,
    schemas: formSchemas,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
    labelCol: { span: 6 },
    wrapperCol: { span: 18 },
  });

  // 注册抽屉
  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
    try {
      setDrawerProps({ confirmLoading: false, loading: true });

      // 重置状态
      resetFields();
      phaseTemplateInfo.value = {};

      isUpdate.value = !!data?.isUpdate;

      if (unref(isUpdate)) {
        phaseTemplateId.value = data.record.id;
        phaseTemplateInfo.value = data.record;
        setFieldsValue({
          ...data.record,
        });
      }
    } catch (error) {
      console.error('初始化抽屉失败:', error);
      createMessage.error('初始化失败');
    } finally {
      setDrawerProps({ loading: false });
    }
  });

  // 获取标题
  const getTitle = computed(() => {
    return unref(isUpdate) ? '编辑阶段模板' : '新增阶段模板';
  });

  // 提交表单
  async function handleSubmit() {
    try {
      const values = await validate();
      loading.value = true;
      setDrawerProps({ confirmLoading: true });

      console.log('提交的表单数据:', values);

      if (unref(isUpdate)) {
        await updatePhaseTemplate(phaseTemplateId.value, values);
        createMessage.success('阶段模板更新成功');
      } else {
        await createPhaseTemplate(values);
        createMessage.success('阶段模板创建成功');
      }

      closeDrawer();
      emit('reload');
    } catch (error) {
      console.error('表单验证失败或提交出错:', error);
      createMessage.error(`操作失败: ${(error as any)?.message || '未知错误'}`);
    } finally {
      loading.value = false;
      setDrawerProps({ confirmLoading: false });
    }
  }
</script>

<style lang="less" scoped>
  .phase-template-form-container {
    padding: 24px;
  }

  :deep(.ant-form) {
    .ant-form-item-label > label {
      font-weight: 500;
    }

    .ant-input-number {
      width: 100%;
    }
  }
</style>
