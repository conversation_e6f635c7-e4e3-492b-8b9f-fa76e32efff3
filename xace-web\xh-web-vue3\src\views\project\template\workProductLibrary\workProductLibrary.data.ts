import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { h } from 'vue';
import { Tag } from 'ant-design-vue';

export const columns: BasicColumn[] = [
  {
    title: '交付物编码',
    dataIndex: 'code',
    width: 120,
    sorter: true,
  },
  {
    title: '交付物名称',
    dataIndex: 'name',
    width: 200,
    sorter: true,
  },
  {
    title: '交付物类型',
    dataIndex: 'typeName',
    width: 120,
  },
  {
    title: '子类型',
    dataIndex: 'subTypeName',
    width: 120,
  },
  {
    title: '默认责任角色',
    dataIndex: 'defaultRoleName',
    width: 120,
  },
  {
    title: '需要评审',
    dataIndex: 'needReviewText',
    width: 80,
    customRender: ({ record }) => {
      const status = record.needReview;
      const color = status === 1 ? 'success' : 'default';
      const text = status === 1 ? '是' : '否';
      return h(Tag, { color }, () => text);
    },
  },
  {
    title: '最终交付成果',
    dataIndex: 'isDeliverableText',
    width: 100,
    customRender: ({ record }) => {
      const status = record.isDeliverable;
      const color = status === 1 ? 'processing' : 'default';
      const text = status === 1 ? '是' : '否';
      return h(Tag, { color }, () => text);
    },
  },
  {
    title: '可裁剪',
    dataIndex: 'canCutText',
    width: 80,
    customRender: ({ record }) => {
      const status = record.canCut;
      const color = status === 1 ? 'warning' : 'default';
      const text = status === 1 ? '可' : '不可';
      return h(Tag, { color }, () => text);
    },
  },
  {
    title: '状态',
    dataIndex: 'statusName',
    width: 80,
    customRender: ({ record }) => {
      const status = record.statusId;
      const color = status === 'enabled' ? 'success' : 'error';
      const text = status === 'enabled' ? '启用' : '禁用';
      return h(Tag, { color }, () => text);
    },
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    width: 180,
    sorter: true,
  },
  {
    title: '创建人',
    dataIndex: 'createdByName',
    width: 100,
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    field: 'keyword',
    label: '关键字',
    component: 'Input',
    componentProps: {
      placeholder: '请输入交付物名称或编码',
    },
    colProps: { span: 8 },
  },
  {
    field: 'typeId',
    label: '交付物类型',
    component: 'Select',
    componentProps: {
      placeholder: '请选择交付物类型',
      options: [
        { label: '文档', value: 'document' },
        { label: '软件', value: 'software' },
        { label: '硬件', value: 'hardware' },
      ],
    },
    colProps: { span: 8 },
  },
  {
    field: 'statusId',
    label: '状态',
    component: 'Select',
    componentProps: {
      placeholder: '请选择状态',
      options: [
        { label: '启用', value: 'enabled' },
        { label: '禁用', value: 'disabled' },
      ],
    },
    colProps: { span: 8 },
  },
  {
    field: 'needReview',
    label: '需要评审',
    component: 'Select',
    componentProps: {
      placeholder: '请选择是否需要评审',
      options: [
        { label: '是', value: 1 },
        { label: '否', value: 0 },
      ],
    },
    colProps: { span: 8 },
  },
  {
    field: 'isDeliverable',
    label: '最终交付成果',
    component: 'Select',
    componentProps: {
      placeholder: '请选择是否为最终交付成果',
      options: [
        { label: '是', value: 1 },
        { label: '否', value: 0 },
      ],
    },
    colProps: { span: 8 },
  },
  {
    field: 'canCut',
    label: '可裁剪',
    component: 'Select',
    componentProps: {
      placeholder: '请选择是否可裁剪',
      options: [
        { label: '可', value: 1 },
        { label: '不可', value: 0 },
      ],
    },
    colProps: { span: 8 },
  },
];

export const formSchema: FormSchema[] = [
  {
    field: 'id',
    label: 'ID',
    component: 'Input',
    show: false,
  },
  {
    field: 'code',
    label: '交付物编码',
    component: 'Input',
    componentProps: {
      placeholder: '请输入交付物编码（可自动生成）',
      maxlength: 50,
    },
    colProps: { span: 12 },
  },
  {
    field: 'name',
    label: '交付物名称',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入交付物名称',
      maxlength: 255,
    },
    colProps: { span: 12 },
  },
  {
    field: 'description',
    label: '描述',
    component: 'InputTextArea',
    componentProps: {
      placeholder: '请输入交付物描述',
      rows: 4,
      maxlength: 1000,
      showCount: true,
    },
    colProps: { span: 24 },
  },
  {
    field: 'typeId',
    label: '交付物类型',
    component: 'Select',
    required: true,
    componentProps: {
      placeholder: '请选择交付物类型',
      options: [
        { label: '文档', value: 'document' },
        { label: '软件', value: 'software' },
        { label: '硬件', value: 'hardware' },
      ],
    },
    colProps: { span: 12 },
  },
  {
    field: 'subTypeId',
    label: '交付物子类型',
    component: 'Select',
    componentProps: {
      placeholder: '请选择交付物子类型',
      options: [
        { label: '需求文档', value: 'requirement' },
        { label: '设计文档', value: 'design' },
        { label: '测试文档', value: 'test' },
      ],
    },
    colProps: { span: 12 },
  },
  {
    field: 'defaultRoleId',
    label: '默认责任角色',
    component: 'Select',
    componentProps: {
      placeholder: '请选择默认责任角色',
      options: [
        { label: '项目经理', value: 'pm' },
        { label: '系统分析师', value: 'sa' },
        { label: '开发工程师', value: 'dev' },
        { label: '测试工程师', value: 'test' },
      ],
    },
    colProps: { span: 12 },
  },
  {
    field: 'statusId',
    label: '状态',
    component: 'RadioButtonGroup',
    defaultValue: 'enabled',
    componentProps: {
      options: [
        { label: '启用', value: 'enabled' },
        { label: '禁用', value: 'disabled' },
      ],
    },
    colProps: { span: 12 },
  },
  {
    field: 'needReview',
    label: '是否需要评审',
    component: 'RadioButtonGroup',
    defaultValue: 0,
    componentProps: {
      options: [
        { label: '是', value: 1 },
        { label: '否', value: 0 },
      ],
    },
    colProps: { span: 8 },
  },
  {
    field: 'isDeliverable',
    label: '是否最终交付成果',
    component: 'RadioButtonGroup',
    defaultValue: 0,
    componentProps: {
      options: [
        { label: '是', value: 1 },
        { label: '否', value: 0 },
      ],
    },
    colProps: { span: 8 },
  },
  {
    field: 'canCut',
    label: '是否可裁剪',
    component: 'RadioButtonGroup',
    defaultValue: 1,
    componentProps: {
      options: [
        { label: '可', value: 1 },
        { label: '不可', value: 0 },
      ],
    },
    colProps: { span: 8 },
  },
];
