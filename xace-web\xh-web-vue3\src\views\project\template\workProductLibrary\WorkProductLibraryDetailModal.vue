<template>
  <BasicModal 
    v-bind="$attrs" 
    @register="registerModal" 
    title="标准交付物库详情" 
    :footer="null"
    width="1000px"
  >
    <div class="workproduct-detail">
      <!-- 基本信息 -->
      <div class="detail-section">
        <h3 class="section-title">基本信息</h3>
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="交付物编码">{{ workProductInfo.code }}</a-descriptions-item>
          <a-descriptions-item label="交付物名称">{{ workProductInfo.name }}</a-descriptions-item>
          <a-descriptions-item label="交付物类型">{{ workProductInfo.typeName }}</a-descriptions-item>
          <a-descriptions-item label="交付物子类型">{{ workProductInfo.subTypeName }}</a-descriptions-item>
          <a-descriptions-item label="默认责任角色">{{ workProductInfo.defaultRoleName }}</a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-tag :color="getStatusColor(workProductInfo.statusId)">
              {{ getStatusText(workProductInfo.statusId) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="描述" :span="2">{{ workProductInfo.description || '暂无描述' }}</a-descriptions-item>
        </a-descriptions>
      </div>

      <!-- 属性信息 -->
      <div class="detail-section">
        <h3 class="section-title">属性信息</h3>
        <a-descriptions :column="3" bordered>
          <a-descriptions-item label="需要评审">
            <a-tag :color="workProductInfo.needReview === 1 ? 'success' : 'default'">
              {{ workProductInfo.needReview === 1 ? '是' : '否' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="最终交付成果">
            <a-tag :color="workProductInfo.isDeliverable === 1 ? 'processing' : 'default'">
              {{ workProductInfo.isDeliverable === 1 ? '是' : '否' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="可裁剪">
            <a-tag :color="workProductInfo.canCut === 1 ? 'warning' : 'default'">
              {{ workProductInfo.canCut === 1 ? '可' : '不可' }}
            </a-tag>
          </a-descriptions-item>
        </a-descriptions>
      </div>

      <!-- 统计信息 -->
      <div class="detail-section">
        <h3 class="section-title">使用统计</h3>
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="使用次数">{{ workProductInfo.usageCount || 0 }}</a-descriptions-item>
          <a-descriptions-item label="关联项目模板数量">{{ workProductInfo.projectTemplateCount || 0 }}</a-descriptions-item>
        </a-descriptions>
      </div>

      <!-- 创建信息 -->
      <div class="detail-section">
        <h3 class="section-title">创建信息</h3>
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="创建时间">{{ workProductInfo.createdAt }}</a-descriptions-item>
          <a-descriptions-item label="创建人">{{ workProductInfo.createdByName }}</a-descriptions-item>
          <a-descriptions-item label="最后修改时间">{{ workProductInfo.lastUpdatedAt }}</a-descriptions-item>
          <a-descriptions-item label="最后修改人">{{ workProductInfo.lastUpdatedByName }}</a-descriptions-item>
        </a-descriptions>
      </div>

      <!-- 关联信息 -->
      <div class="detail-section" v-if="workProductInfo.defaultApprovalName || workProductInfo.defaultChecklistName">
        <h3 class="section-title">关联信息</h3>
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="默认审批流程" v-if="workProductInfo.defaultApprovalName">
            {{ workProductInfo.defaultApprovalName }}
          </a-descriptions-item>
          <a-descriptions-item label="默认检查单模板" v-if="workProductInfo.defaultChecklistName">
            {{ workProductInfo.defaultChecklistName }}
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { getWorkProductLibraryDetailInfo } from '/@/api/project/workProductLibrary';

  defineOptions({ name: 'WorkProductLibraryDetailModal' });

  const workProductInfo = ref<any>({});

  const [registerModal, { setModalProps }] = useModalInner(async (data) => {
    setModalProps({ confirmLoading: true });
    
    try {
      if (data?.record) {
        const result = await getWorkProductLibraryDetailInfo(data.record.id);
        workProductInfo.value = result.data || result;
      }
    } catch (error) {
      console.error('获取交付物详情失败:', error);
    } finally {
      setModalProps({ confirmLoading: false });
    }
  });

  function getStatusColor(status: string) {
    const colorMap = {
      'enabled': 'success',
      'disabled': 'error',
    };
    return colorMap[status] || 'default';
  }

  function getStatusText(status: string) {
    const textMap = {
      'enabled': '启用',
      'disabled': '禁用',
    };
    return textMap[status] || status;
  }
</script>

<style lang="less" scoped>
  .workproduct-detail {
    .detail-section {
      margin-bottom: 24px;
      
      .section-title {
        margin-bottom: 16px;
        font-size: 16px;
        font-weight: 600;
        color: #262626;
        border-left: 4px solid #1890ff;
        padding-left: 12px;
      }
    }
  }
</style>
