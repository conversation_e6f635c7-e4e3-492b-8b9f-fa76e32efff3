<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinghuo.project.core.dao.ProjectUserInteractionMapper">

    <!-- 用户项目交互基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.xinghuo.project.core.entity.ProjectUserInteractionEntity">
        <id column="f_id" property="id" jdbcType="VARCHAR"/>
        <result column="project_id" property="projectId" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="interaction_type" property="interactionType" jdbcType="VARCHAR"/>
        <result column="interaction_status" property="interactionStatus" jdbcType="INTEGER"/>
        <result column="last_interaction_time" property="lastInteractionTime" jdbcType="TIMESTAMP"/>
        <result column="interaction_count" property="interactionCount" jdbcType="INTEGER"/>
        <result column="interaction_content" property="interactionContent" jdbcType="VARCHAR"/>
        <result column="interaction_source" property="interactionSource" jdbcType="VARCHAR"/>
        <result column="ip_address" property="ipAddress" jdbcType="VARCHAR"/>
        <result column="user_agent" property="userAgent" jdbcType="VARCHAR"/>
        <result column="extend_info" property="extendInfo" jdbcType="VARCHAR"/>
        <result column="remarks" property="remarks" jdbcType="VARCHAR"/>
        <result column="f_created_at" property="createdAt" jdbcType="TIMESTAMP"/>
        <result column="f_updated_at" property="updatedAt" jdbcType="TIMESTAMP"/>
        <result column="f_created_by" property="createdBy" jdbcType="VARCHAR"/>
        <result column="f_updated_by" property="updatedBy" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础SQL片段 -->
    <sql id="Base_Column_List">
        pui.f_id, pui.project_id, pui.user_id, pui.interaction_type, pui.interaction_status,
        pui.last_interaction_time, pui.interaction_count, pui.interaction_content, pui.interaction_source,
        pui.ip_address, pui.user_agent, pui.extend_info, pui.remarks,
        pui.f_created_at, pui.f_updated_at, pui.f_created_by, pui.f_updated_by
    </sql>

    <!-- 根据用户ID和交互类型查询项目ID列表 -->
    <select id="selectProjectIdsByUserIdAndType" resultType="string">
        SELECT project_id
        FROM zz_proj_user_interaction
        WHERE user_id = #{userId} 
        AND interaction_type = #{interactionType}
        AND interaction_status = #{interactionStatus}
        ORDER BY last_interaction_time DESC
    </select>

    <!-- 根据用户ID查询最近访问的项目 -->
    <select id="selectRecentlyVisitedProjects" resultType="string">
        SELECT project_id
        FROM zz_proj_user_interaction
        WHERE user_id = #{userId} 
        AND interaction_type = 'visit'
        AND interaction_status = 1
        <if test="days != null">
            AND last_interaction_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY)
        </if>
        ORDER BY last_interaction_time DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 根据项目ID和用户ID查询交互记录 -->
    <select id="selectByProjectIdAndUserIdAndType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM zz_proj_user_interaction pui
        WHERE pui.project_id = #{projectId} 
        AND pui.user_id = #{userId}
        AND pui.interaction_type = #{interactionType}
    </select>

    <!-- 更新或插入交互记录 -->
    <insert id="insertOrUpdate">
        INSERT INTO zz_proj_user_interaction (
            f_id, project_id, user_id, interaction_type, interaction_status,
            last_interaction_time, interaction_count, interaction_content, interaction_source,
            ip_address, user_agent, extend_info, remarks, f_created_at, f_created_by
        ) VALUES (
            #{id}, #{projectId}, #{userId}, #{interactionType}, #{interactionStatus},
            #{lastInteractionTime}, #{interactionCount}, #{interactionContent}, #{interactionSource},
            #{ipAddress}, #{userAgent}, #{extendInfo}, #{remarks}, NOW(), #{createdBy}
        ) ON DUPLICATE KEY UPDATE
            interaction_status = VALUES(interaction_status),
            last_interaction_time = VALUES(last_interaction_time),
            interaction_count = interaction_count + 1,
            interaction_content = VALUES(interaction_content),
            f_updated_at = NOW(),
            f_updated_by = VALUES(f_created_by)
    </insert>

    <!-- 更新交互状态 -->
    <update id="updateInteractionStatus">
        UPDATE zz_proj_user_interaction 
        SET interaction_status = #{interactionStatus}, 
            last_interaction_time = NOW(),
            f_updated_at = NOW()
        WHERE project_id = #{projectId} 
        AND user_id = #{userId}
        AND interaction_type = #{interactionType}
    </update>

    <!-- 增加交互次数 -->
    <update id="incrementInteractionCount">
        UPDATE zz_proj_user_interaction 
        SET interaction_count = interaction_count + 1,
            last_interaction_time = NOW(),
            f_updated_at = NOW()
        WHERE project_id = #{projectId} 
        AND user_id = #{userId}
        AND interaction_type = #{interactionType}
    </update>

    <!-- 获取项目的交互统计 -->
    <select id="getProjectInteractionStatistics" resultType="map">
        SELECT 
            interaction_type,
            COUNT(CASE WHEN interaction_status = 1 THEN 1 END) as active_count,
            COUNT(1) as total_count,
            SUM(interaction_count) as total_interactions,
            MAX(last_interaction_time) as latest_interaction_time
        FROM zz_proj_user_interaction
        WHERE project_id = #{projectId}
        GROUP BY interaction_type
    </select>

    <!-- 获取用户的交互统计 -->
    <select id="getUserInteractionStatistics" resultType="map">
        SELECT 
            interaction_type,
            COUNT(CASE WHEN interaction_status = 1 THEN 1 END) as active_count,
            COUNT(1) as total_count,
            SUM(interaction_count) as total_interactions,
            MAX(last_interaction_time) as latest_interaction_time
        FROM zz_proj_user_interaction
        WHERE user_id = #{userId}
        GROUP BY interaction_type
    </select>

    <!-- 根据项目ID查询关注用户列表 -->
    <select id="selectFollowersByProjectId" resultType="string">
        SELECT user_id
        FROM zz_proj_user_interaction
        WHERE project_id = #{projectId} 
        AND interaction_type = 'follow'
        AND interaction_status = 1
        ORDER BY last_interaction_time DESC
    </select>

    <!-- 根据项目ID查询收藏用户列表 -->
    <select id="selectFavoriteUsersByProjectId" resultType="string">
        SELECT user_id
        FROM zz_proj_user_interaction
        WHERE project_id = #{projectId} 
        AND interaction_type = 'favorite'
        AND interaction_status = 1
        ORDER BY last_interaction_time DESC
    </select>

    <!-- 检查用户是否关注项目 -->
    <select id="checkUserFollowProject" resultType="int">
        SELECT COUNT(1)
        FROM zz_proj_user_interaction
        WHERE project_id = #{projectId} 
        AND user_id = #{userId}
        AND interaction_type = 'follow'
        AND interaction_status = 1
    </select>

    <!-- 检查用户是否收藏项目 -->
    <select id="checkUserFavoriteProject" resultType="int">
        SELECT COUNT(1)
        FROM zz_proj_user_interaction
        WHERE project_id = #{projectId} 
        AND user_id = #{userId}
        AND interaction_type = 'favorite'
        AND interaction_status = 1
    </select>

    <!-- 删除用户的所有交互记录 -->
    <delete id="deleteByUserId">
        DELETE FROM zz_proj_user_interaction WHERE user_id = #{userId}
    </delete>

    <!-- 删除项目的所有交互记录 -->
    <delete id="deleteByProjectId">
        DELETE FROM zz_proj_user_interaction WHERE project_id = #{projectId}
    </delete>

    <!-- 获取热门项目（基于访问次数） -->
    <select id="selectHotProjects" resultType="string">
        SELECT project_id
        FROM zz_proj_user_interaction
        WHERE interaction_type = 'visit'
        AND interaction_status = 1
        <if test="days != null">
            AND last_interaction_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY)
        </if>
        GROUP BY project_id
        ORDER BY SUM(interaction_count) DESC, MAX(last_interaction_time) DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 获取用户访问历史 -->
    <select id="selectUserVisitHistory" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM zz_proj_user_interaction pui
        WHERE pui.user_id = #{userId}
        AND pui.interaction_type = 'visit'
        <if test="startTime != null">
            AND pui.last_interaction_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND pui.last_interaction_time <= #{endTime}
        </if>
        ORDER BY pui.last_interaction_time DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 清理过期的访问记录 -->
    <delete id="cleanExpiredVisitRecords">
        DELETE FROM zz_proj_user_interaction 
        WHERE interaction_type = 'visit'
        AND last_interaction_time < #{beforeDate}
    </delete>

    <!-- 批量插入交互记录 -->
    <insert id="batchInsert">
        INSERT INTO zz_proj_user_interaction (
            f_id, project_id, user_id, interaction_type, interaction_status,
            last_interaction_time, interaction_count, interaction_content, interaction_source,
            ip_address, user_agent, extend_info, remarks, f_created_at, f_created_by
        ) VALUES
        <foreach collection="interactions" item="item" separator=",">
            (
                #{item.id}, #{item.projectId}, #{item.userId}, #{item.interactionType}, #{item.interactionStatus},
                #{item.lastInteractionTime}, #{item.interactionCount}, #{item.interactionContent}, #{item.interactionSource},
                #{item.ipAddress}, #{item.userAgent}, #{item.extendInfo}, #{item.remarks}, NOW(), #{item.createdBy}
            )
        </foreach>
    </insert>

</mapper>
