<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinghuo.manhour.dao.ManhourProjectMapper">

    <select id="getProjAndModuleList" resultType="com.xinghuo.manhour.entity.ManhourProjectEntity">
        select * from zz_proj_manhour_project_auto where WORK_STATUS=1
        and project_no is not null
        and f_id not in (
            select f_id from zz_proj_manhour_project_auto
            where project_id in (
            select project_id from zz_proj_manhour_project_auto t
            where work_status=1
            and module_id is not null
            )
            and module_id is null
            and work_status=1
        )
        order by project_id desc
    </select>



</mapper>
