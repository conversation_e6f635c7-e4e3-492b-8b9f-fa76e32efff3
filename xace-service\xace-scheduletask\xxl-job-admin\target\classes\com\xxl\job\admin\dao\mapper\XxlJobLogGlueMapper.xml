<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xxl.job.admin.dao.XxlJobLogGlueMapper">

    <resultMap id="XxlJobLogGlue" type="com.xxl.job.admin.core.model.XxlJobLogGlue">
        <result column="id" property="id"/>
        <result column="job_id" property="jobId"/>
        <result column="glue_type" property="glueType"/>
        <result column="glue_source" property="glueSource"/>
        <result column="glue_remark" property="glueRemark"/>
        <result column="add_time" property="addTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
		t.id,
		t.job_id,
		t.glue_type,
		t.glue_source,
		t.glue_remark,
		t.add_time,
		t.update_time
	</sql>

</mapper>
