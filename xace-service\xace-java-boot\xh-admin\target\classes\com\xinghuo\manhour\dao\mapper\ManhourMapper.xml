<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinghuo.manhour.dao.ManhourMapper">
    <resultMap id="ManhourEntity" type="com.xinghuo.manhour.entity.ManhourEntity">
        <result column="proj_type" property="projType"/>
        <result column="project_id" property="projectId"/>
        <result column="project_name" property="projectName"/>
        <result column="module_id" property="moduleId"/>
        <result column="module_name" property="moduleName"/>
        <result column="work_type" property="workType"/>
        <result column="month" property="month"/>
        <result column="user_id" property="userId"/>
        <result column="work_month" property="workMonth"/>
        <result column="work_note" property="workNote"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <resultMap id="JiraWorkLog" type="com.xinghuo.manhour.model.jira.JiraWorkLog">
        <result column="id" property="id"/>
        <result column="pname" property="projectName"/>
        <result column="pkey" property="projectKey"/>
        <result column="taskkey" property="taskKey"/>
        <result column="summary" property="taskName"/>
        <result column="worklogbody" property="worklogbody"/>
        <result column="startdate" property="startDate"/>
        <result column="created" property="createDate"/>
        <result column="timeworked" property="timeworked"/>
    </resultMap>
    <resultMap id="CommitLog" type="com.xinghuo.manhour.model.jira.CommitLog">
        <result column="id" property="id"/>
        <result column="project_key" property="projectKey"/>
        <result column="repository_slug" property="repositorySlug"/>
        <result column="author_name" property="authorName"/>
        <result column="author_time" property="authorTime"/>
        <result column="committer_name" property="committerName"/>
        <result column="committer_time" property="committerTime"/>
        <result column="message" property="message"/>
    </resultMap>

    <resultMap id="EventLog" type="com.xinghuo.manhour.model.jira.EventLog">
        <result column="eventId" property="id"/>
        <result column="project_id" property="projectId"/>
        <result column="pm_name" property="projectName"/>
        <result column="event_descri" property="eventDesc"/>
        <result column="deal_user_id" property="dealUserId"/>
        <result column="deal_user_name" property="dealUserName"/>
        <result column="create_date" property="createDate"/>
        <result column="handle_time" property="handleTime"/>
        <result column="proj_module_id" property="projMdouleId"/>
    </resultMap>

    <select id="getMyValidList" parameterType="String"  resultMap="ManhourEntity">
        SELECT DISTINCT pm.proj_type, pm.project_id, pm.project_name, pm.module_id, pm.module_name
        FROM  zz_proj_manhour pm   JOIN  view_zz_workhour_project p ON p.f_id = pm.project_id
                                      left JOIN  zz_proj_manhour_module m ON m.F_ID = pm.module_id
        WHERE    p.work_status = 1    	and pm.user_id = #{userId}
    </select>

    <select id="getMigrationList" parameterType="String"  resultMap="ManhourEntity">
        SELECT  pm.user_id,  pm.proj_type, pm.project_id, pm.project_name, pm.module_id, pm.module_name,
        pm.work_type,pm.month,pm.work_month,pm.work_note,pm.create_user_id,pm.create_time
        FROM  zz_proj_manhour pm   where pm.f_id in (select manhour_id from zz_proj_manhour_migration_detail where migration_id=#{migrationId})
    </select>

    <select id="getMonthJiraWorkLog" parameterType="String"  resultMap="JiraWorkLog">
       select p.pname,p.pkey,x.id,concat(p.pkey,"-",issuenum) taskkey,  x.SUMMARY,x.worklogbody,x.STARTDATE,x.CREATED,x.timeworked from
(select i.id,i.PROJECT,i.issuenum,  i.SUMMARY,w.worklogbody,w.STARTDATE,w.CREATED,w.UPDATED,round(w.timeworked/3600,2) timeworked from synced_jira_worklog w left join synced_jira_issue i on w.issueid=i.ID where author=#{account}
and DATE_FORMAT(startdate,'%Y%m')=#{month}) x left join synced_jira_project p on p.id= x.project order by x.STARTDATE asc    </select>

    <select id="getYbMonthJiraWorkLog" parameterType="String"  resultMap="JiraWorkLog">
        select p.pname,p.pkey,x.id,concat(p.pkey,"-",issuenum) taskkey,  x.SUMMARY,x.worklogbody,x.STARTDATE,x.CREATED,x.timeworked from
            (select i.id,i.PROJECT,i.issuenum,  i.SUMMARY,w.worklogbody,w.STARTDATE,w.CREATED,w.UPDATED,round(w.timeworked/3600,2) timeworked from synced_yb_worklog w left join synced_yb_jiraissue i on w.issueid=i.ID where author=#{account}
        and DATE_FORMAT(startdate,'%Y%m')=#{month}) x left join synced_yb_project p on p.id= x.project order by x.STARTDATE asc    </select>

    <select id="getMonthCommitLog" parameterType="String"  resultMap="CommitLog">
       select id,project_key,repository_slug,author_name,author_time,committer_name,committer_time,message from synced_bitbucket_commit
where DATE_FORMAT(committer_time,'%Y%m')=#{month}  and author_name=#{account} order by author_time asc </select>

    <select id="getMonthEventLog" parameterType="String"  resultMap="EventLog">
       select *, ifnull(module_id,project_id) proj_module_id from zz_proj_event
where DATE_FORMAT(create_date,'%Y%m')=#{month}  and deal_user_id=#{userId} order by create_date asc </select>

    <select id="getMonthWorkDayCount" parameterType="String"  resultType="java.lang.Integer">
       select count(0) from zz_oa_date d where DATE_FORMAT(d.curdate,'%Y%m')=#{month}  and is_holiday=0 </select>

</mapper>
