package com.xinghuo.project.template.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 标准交付物库视图对象
 * 用于返回交付物库列表和详情信息
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
@Data
@Schema(description = "标准交付物库视图对象")
public class WorkProductLibraryVO {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private String id;

    /**
     * 交付物编码
     */
    @Schema(description = "交付物编码")
    private String code;

    /**
     * 交付物名称
     */
    @Schema(description = "交付物名称")
    private String name;

    /**
     * 描述
     */
    @Schema(description = "描述")
    private String description;

    /**
     * 交付物类型ID
     */
    @Schema(description = "交付物类型ID")
    private String typeId;

    /**
     * 交付物类型名称
     */
    @Schema(description = "交付物类型名称")
    private String typeName;

    /**
     * 交付物子类型ID
     */
    @Schema(description = "交付物子类型ID")
    private String subTypeId;

    /**
     * 交付物子类型名称
     */
    @Schema(description = "交付物子类型名称")
    private String subTypeName;

    /**
     * 默认责任角色ID
     */
    @Schema(description = "默认责任角色ID")
    private String defaultRoleId;

    /**
     * 默认责任角色名称
     */
    @Schema(description = "默认责任角色名称")
    private String defaultRoleName;

    /**
     * 是否需要评审 (1:是, 0:否)
     */
    @Schema(description = "是否需要评审")
    private Integer needReview;

    /**
     * 是否需要评审的文本描述
     */
    @Schema(description = "是否需要评审的文本描述")
    private String needReviewText;

    /**
     * 是否是项目最终交付成果 (1:是, 0:否)
     */
    @Schema(description = "是否是项目最终交付成果")
    private Integer isDeliverable;

    /**
     * 是否是项目最终交付成果的文本描述
     */
    @Schema(description = "是否是项目最终交付成果的文本描述")
    private String isDeliverableText;

    /**
     * 是否可裁剪 (1:可, 0:不可)
     */
    @Schema(description = "是否可裁剪")
    private Integer canCut;

    /**
     * 是否可裁剪的文本描述
     */
    @Schema(description = "是否可裁剪的文本描述")
    private String canCutText;

    /**
     * 状态ID
     */
    @Schema(description = "状态ID")
    private String statusId;

    /**
     * 状态名称
     */
    @Schema(description = "状态名称")
    private String statusName;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private String tenantId;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    /**
     * 创建用户ID
     */
    @Schema(description = "创建用户ID")
    private String createdBy;

    /**
     * 创建用户名称
     */
    @Schema(description = "创建用户名称")
    private String createdByName;

    /**
     * 最后修改时间
     */
    @Schema(description = "最后修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastUpdatedAt;

    /**
     * 最后修改用户ID
     */
    @Schema(description = "最后修改用户ID")
    private String lastUpdatedBy;

    /**
     * 最后修改用户名称
     */
    @Schema(description = "最后修改用户名称")
    private String lastUpdatedByName;

    /**
     * 使用次数统计
     */
    @Schema(description = "使用次数统计")
    private Integer usageCount;

    /**
     * 关联项目模板数量
     */
    @Schema(description = "关联项目模板数量")
    private Integer projectTemplateCount;
}
