<template>
  <div class="xh-content-wrapper">
    <div class="xh-content-wrapper-center">
      <div class="xh-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #tableTitle>
            <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="addOrUpdateHandle()">{{ t('common.addText') }}</a-button>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'tagName'">
              <a-tag :color="record.tagColor">{{ record.tagName }}</a-tag>
            </template>
            <template v-if="column.key === 'scope'">
              <a-tag :color="record.scope === 'global' ? 'blue' : 'purple'">
                {{ record.scope === 'global' ? '全局可见' : '特定用户可见' }}
              </a-tag>
            </template>
            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <Form @register="registerForm" @reload="reload" />
  </div>
</template>

<script lang="ts" setup>
  import { h } from 'vue';
  import { BasicTable, useTable, TableAction,ActionItem} from '/@/components/Table';
  import { getTagList, deleteTag, TagModel } from '/@/api/project/tag';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useModal } from '/@/components/Modal';
  import { FormSchema } from '/@/components/Form';
  import Form from './Form.vue';

  const { t } = useI18n();
  const { createMessage } = useMessage();
  const [registerForm, { openModal }] = useModal();
  defineOptions({ name: 'project-core-tag' });

  // 表格列定义
  const columns = [
    {
      title: '标签名称',
      key: 'tagName',
      width: 150,
    },
    {
      title: '标签颜色',
      dataIndex: 'tagColor',
      width: 100,
      customRender: ({ text }) => {
        return h('div', { style: { width: '20px', height: '20px', backgroundColor: text, borderRadius: '4px' } });
      },
    },
    {
      title: '标签描述',
      dataIndex: 'description',
      width: 300,
    },
    {
      title: '标签范围',
      key: 'scope',
      width: 120,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      width: 180,
    },
    {
      title: '创建人',
      dataIndex: 'createdBy',
      width: 120,
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      fixed: 'right' as const,
    },
  ];

  // 搜索表单配置
  const searchFormSchema: FormSchema[] = [
    {
      field: 'tagName',
      label: '标签名称',
      component: 'Input',
      colProps: { span: 6 },
    },
    {
      field: 'scope',
      label: '标签范围',
      component: 'Select',
      componentProps: {
        options: [
          { fullName: '全部', id: '' },
          { fullName: '全局可见', id: 'global' },
          { fullName: '特定用户可见', id: 'user_specific' },
        ],
      },
      colProps: { span: 6 },
    },
  ];

  // 注册表格
  const [registerTable, { reload }] = useTable({
    title: '标签列表',
    api: getTagList,
    columns,
    formConfig: {
      labelWidth: 80,
      schemas: searchFormSchema,
    },
    useSearchForm: true,
    showTableSetting: true,
    bordered: true,
    showIndexColumn: true,
    canResize: true,
  });

  // 获取表格操作按钮
  function getTableActions(record: TagModel): ActionItem[] {
    return [
      {
        label: t('common.editText'),
        onClick: () => addOrUpdateHandle(record),
      },
      {
        label: t('common.delText'),
        color: 'error',
        popConfirm: {
          title: t('common.delTip'),
          confirm: handleDelete.bind(null, record),
        },
      },
    ];
  }

  // 新增或编辑
  function addOrUpdateHandle(record?: TagModel) {
    openModal(true, {
      record,
      isUpdate: !!record,
    });
  }

  // 删除
  async function handleDelete(record: TagModel) {
    try {
      await deleteTag(record.id);
      createMessage.success(t('common.delSuccessText'));
      reload();
    } catch (error) {
      console.error('删除失败:', error);
    }
  }
</script>
