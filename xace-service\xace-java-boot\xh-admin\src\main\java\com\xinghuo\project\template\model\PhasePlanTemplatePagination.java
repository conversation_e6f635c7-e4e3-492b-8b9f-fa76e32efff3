package com.xinghuo.project.template.model;

import com.xinghuo.common.base.model.Pagination;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 阶段计划模板分页查询参数
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PhasePlanTemplatePagination extends Pagination {

    /**
     * 模板名称
     */
    private String name;

    /**
     * 知识状态ID
     */
    private String knStatusId;

    /**
     * 关键字搜索（名称或描述）
     */
    private String keyword;

    /**
     * 创建时间开始
     */
    private Date createTimeStart;

    /**
     * 创建时间结束
     */
    private Date createTimeEnd;

    /**
     * 创建用户ID
     */
    private String creatorUserId;

    /**
     * 标签ID列表
     */
    private List<String> tagIds;

    /**
     * 关联的项目模板ID
     */
    private String projectTemplateId;

    /**
     * 是否包含阶段明细统计信息
     */
    private Boolean includePhaseStats;
}
