package com.xinghuo.project.template.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 阶段模板视图对象
 * 用于返回阶段模板列表和详情信息
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
@Data
@Schema(description = "阶段模板视图对象")
public class PhaseTemplateVO {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private String id;

    /**
     * 阶段编码 (如: PL00000004)
     */
    @Schema(description = "阶段编码")
    private String code;

    /**
     * 阶段名称 (如: 总结与评审)
     */
    @Schema(description = "阶段名称")
    private String name;

    /**
     * 阶段描述 (如: 总结项目成果...)
     */
    @Schema(description = "阶段描述")
    private String description;

    /**
     * 标准工期(天)
     */
    @Schema(description = "标准工期(天)")
    private Integer stdDuration;

    /**
     * 默认阶段完成审批流程ID (关联审批模板表)
     */
    @Schema(description = "默认阶段完成审批流程ID")
    private String defaultApprovalId;

    /**
     * 默认检查单模板ID (关联检查单模板表)
     */
    @Schema(description = "默认检查单模板ID")
    private String defaultChecklistId;

    /**
     * 状态 (1:启用, 0:禁用)
     */
    @Schema(description = "状态")
    private Integer status;

    /**
     * 状态名称 (启用/禁用)
     */
    @Schema(description = "状态名称")
    private String statusName;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private String tenantId;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    /**
     * 创建用户ID
     */
    @Schema(description = "创建用户ID")
    private String createdBy;

    /**
     * 创建用户名称 (冗余字段，便于显示)
     */
    @Schema(description = "创建用户名称")
    private String createdByName;

    /**
     * 最后修改时间
     */
    @Schema(description = "最后修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastUpdatedAt;

    /**
     * 最后修改用户ID
     */
    @Schema(description = "最后修改用户ID")
    private String lastUpdatedBy;

    /**
     * 最后修改用户名称 (冗余字段，便于显示)
     */
    @Schema(description = "最后修改用户名称")
    private String lastUpdatedByName;

    /**
     * 默认审批流程名称 (冗余字段，便于显示)
     */
    @Schema(description = "默认审批流程名称")
    private String defaultApprovalName;

    /**
     * 默认检查单模板名称 (冗余字段，便于显示)
     */
    @Schema(description = "默认检查单模板名称")
    private String defaultChecklistName;
}
