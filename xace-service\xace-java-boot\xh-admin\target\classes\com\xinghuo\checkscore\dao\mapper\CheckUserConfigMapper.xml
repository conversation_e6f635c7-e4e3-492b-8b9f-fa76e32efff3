<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinghuo.checkscore.dao.CheckUserConfigMapper">
    <resultMap id="UserEntityResultMap" type="com.xinghuo.permission.entity.UserEntity">
        <id property="id" column="f_id" />
        <result property="realName" column="f_realname" />
        <result property="fbId" column="f_fb_id" />
    </resultMap>

    <!---->
      <delete id="truncateCheckWorkDetailTable">
        delete from zz_jx_check_work_detail;
    </delete>

    <delete id="truncateCheckUserRatioTable">
        delete from zz_jx_check_user_ratio;
    </delete>

    <delete id="truncateCheckUserConfigTable">
        delete from zz_jx_check_user_config;
    </delete>

    <select id="countCheckUserConfig" resultType="java.lang.Integer">
        <![CDATA[
        SELECT COUNT(*)
        FROM zz_jx_check_user_config
        WHERE f_id != #{id}
          AND user_id = #{userId}
          AND (
            ( start_date<=#{startDate} AND end_date>=#{startDate} )
                OR ( start_date<=#{endDate} AND end_date>=#{endDate} )
                OR ( start_date>#{startDate} AND end_date<#{endDate} )
            )
        ]]>
    </select>

    <select id="reviewMonthUserCount" resultType="java.lang.Integer">
        SELECT COUNT(distinct user_id)
        FROM zz_jx_check_user_config
    </select>

    <select id="errorCheckUserNames" resultType="java.lang.String">
        SELECT f_realname
        FROM base_user
        WHERE f_id IN (SELECT DISTINCT user_id
                       FROM zz_jx_check_user_config)
          AND f_id NOT IN (SELECT f_id
                           FROM base_user
                           WHERE review_status = 1
                             AND F_DeleteMark IS NULL
                             AND F_EnabledMark > 0)
        ORDER BY f_realname
    </select>


    <select id="getCurrentCheckUserList" resultMap="UserEntityResultMap">
        <![CDATA[
        SELECT f_id, f_realname,f_fb_id
        FROM base_user
        WHERE f_id IN (SELECT DISTINCT user_id
                       FROM zz_jx_check_user_config)
        ]]>
    </select>
</mapper>