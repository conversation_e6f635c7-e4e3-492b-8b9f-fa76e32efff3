<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinghuo.workflow.dao.OtLeaveApplyMapper">
    <select id="remainHours" parameterType="String" resultType="java.lang.Double">
        select CASE WHEN IFNULL(remain_hours, 0) > 23 THEN 23 ELSE IFNULL(remain_hours, 0)  END AS adjusted_remain_hours
        from view_overtime_remainhours where user_id= #{userId}
    </select>

</mapper>
