package com.xinghuo.project.biz.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.util.core.RandomUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.project.biz.dao.BizContractMoneyMapper;
import com.xinghuo.project.biz.entity.BizContractEntity;
import com.xinghuo.project.biz.entity.BizContractMoneyEntity;
import com.xinghuo.project.biz.model.bizContractMoney.BizContractMoneyForm;
import com.xinghuo.project.biz.model.bizContractMoney.BizContractMoneyPagination;
import com.xinghuo.project.biz.service.BizContractMoneyService;
import com.xinghuo.project.biz.service.BizContractService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

/**
 * 合同收款服务实现类
 */
@Service
public class BizContractMoneyServiceImpl extends BaseServiceImpl<BizContractMoneyMapper, BizContractMoneyEntity> implements BizContractMoneyService {

    @Autowired
    private BizContractService contractService;

    @Override
    public List<BizContractMoneyEntity> getList(BizContractMoneyPagination pagination) {
        QueryWrapper<BizContractMoneyEntity> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<BizContractMoneyEntity> lambda = queryWrapper.lambda();

        // 根据合同ID精确查询
        if (StrXhUtil.isNotEmpty(pagination.getCid())) {
            lambda.eq(BizContractMoneyEntity::getCid, pagination.getCid());
        }

        // 根据合同名称模糊查询（需要关联查询）
        // 根据合同名称模糊查询（需要关联查询）
        if (StrXhUtil.isNotEmpty(pagination.getContractName())) {
            String keyword = pagination.getContractName().trim();
            String subQuery = String.format(
                    "SELECT c_id FROM zz_proj_contract pc WHERE pc.name LIKE '%%%s%%' OR pc.c_no LIKE '%%%s%%'",
                    keyword, keyword
            );
            queryWrapper.inSql("c_id", subQuery);
        }

        if (pagination.getDateType() != null && pagination.getStartTime() != null) {
            queryWrapper.ge(pagination.getDateType(), pagination.getStartTime());
        }
        if (pagination.getDateType() != null && pagination.getEndTime() != null) {
            queryWrapper.le(pagination.getDateType(), pagination.getEndTime());
        }

        // 根据收款状态精确查询
        if (StrXhUtil.isNotEmpty(pagination.getPayStatus())) {
            lambda.eq(BizContractMoneyEntity::getPayStatus, pagination.getPayStatus());
        }


        // 排序
        lambda.orderByDesc(BizContractMoneyEntity::getYingshouDate);

        // 分页
        return processDataType(queryWrapper, pagination);
    }

    @Override
    public List<BizContractMoneyEntity> getListByContractId(String contractId) {
        LambdaQueryWrapper<BizContractMoneyEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BizContractMoneyEntity::getCid, contractId);
        queryWrapper.orderByAsc(BizContractMoneyEntity::getYingshouDate);
        return this.list(queryWrapper);
    }

    @Override
    public BizContractMoneyEntity getInfo(String id) {
        return this.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(BizContractMoneyEntity entity) {
        entity.setCmId(RandomUtil.snowId());

        // 设置初始状态
        if (StrXhUtil.isEmpty(entity.getPayStatus())) {
            entity.setPayStatus("unpaid"); // 未收款
        }

        this.save(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(String id, BizContractMoneyEntity entity) {
        entity.setCmId(id);
        this.updateById(entity);
    }

    @Override
    public void delete(String id) {
        this.removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(String id, BizContractMoneyForm form) {
        BizContractMoneyEntity entity = this.getById(id);
        if (entity == null) {
            throw new RuntimeException("合同收款不存在");
        }

        // 更新状态
        entity.setPayStatus(form.getPayStatus());
        entity.setKaipiaoDate(form.getKaipiaoDate());
        entity.setShoukuanDate(form.getShoukuanDate());
        entity.setLastNote(form.getLastNote());

        this.updateById(entity);

        // 如果状态为已收款，更新合同的已收金额
        if ("paid".equals(form.getPayStatus()) && form.getShoukuanDate() != null) {
            updateContractAmount(entity);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void registerInvoice(String id, Date kaipiaoDate, String lastNote) {
        BizContractMoneyEntity entity = this.getById(id);
        if (entity == null) {
            throw new RuntimeException("合同收款不存在");
        }

        // 更新开票日期和状态
        entity.setKaipiaoDate(kaipiaoDate);
        entity.setPayStatus("invoiced"); // 已开票
        entity.setLastNote(lastNote);

        this.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void registerPayment(String id, Date shoukuanDate, String lastNote) {
        BizContractMoneyEntity entity = this.getById(id);
        if (entity == null) {
            throw new RuntimeException("合同收款不存在");
        }

        // 更新收款日期和状态
        entity.setShoukuanDate(shoukuanDate);
        entity.setPayStatus("paid"); // 已收款
        entity.setLastNote(lastNote);

        this.updateById(entity);

        // 更新合同的已收金额
        updateContractAmount(entity);
    }

    /**
     * 更新合同的已收金额
     *
     * @param entity 合同收款实体
     */
    private void updateContractAmount(BizContractMoneyEntity entity) {
        BizContractEntity contract = contractService.getById(entity.getCid());
        if (contract == null) {
            return;
        }

        // 更新合同的已收金额
        BigDecimal ysAmount = contract.getYsAmount() != null ? contract.getYsAmount() : BigDecimal.ZERO;
        ysAmount = ysAmount.add(entity.getCmMoney());
        contract.setYsAmount(ysAmount);

        // 如果收款日期是当前年度，更新本年度收款金额
        if (entity.getShoukuanDate() != null) {
            Calendar calendar = Calendar.getInstance();
            int currentYear = calendar.get(Calendar.YEAR);

            calendar.setTime(entity.getShoukuanDate());
            int paymentYear = calendar.get(Calendar.YEAR);

            if (currentYear == paymentYear) {
                BigDecimal yearYsAmount = contract.getYearYsAmount() != null ? contract.getYearYsAmount() : BigDecimal.ZERO;
                yearYsAmount = yearYsAmount.add(entity.getCmMoney());
                contract.setYearYsAmount(yearYsAmount);
            }
        }

        // 更新合同的收款状态
        if (ysAmount.compareTo(contract.getAmount()) >= 0) {
            contract.setMoneyStatus("paid"); // 已结清
        } else if (ysAmount.compareTo(BigDecimal.ZERO) > 0) {
            contract.setMoneyStatus("partial"); // 部分收款
        } else {
            contract.setMoneyStatus("unpaid"); // 未收款
        }

        contractService.updateById(contract);
    }

    @Override
    public Map<String, Object> getStatistics() {
        Map<String, Object> statistics = new HashMap<>();

        // 获取当前日期
        Date now = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(now);

        // 计算本年度开始时间
        calendar.set(Calendar.MONTH, Calendar.JANUARY);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date yearStart = calendar.getTime();

        // 计算本年度结束时间
        calendar.set(Calendar.MONTH, Calendar.DECEMBER);
        calendar.set(Calendar.DAY_OF_MONTH, 31);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        Date yearEnd = calendar.getTime();

        // 计算一个月前的时间（从当前日期往前推30天）
        calendar.setTime(now);
        calendar.add(Calendar.DAY_OF_MONTH, -30);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date oneMonthAgo = calendar.getTime();

        // 计算三个月前的时间（从当前日期往前推90天）
        calendar.setTime(now);
        calendar.add(Calendar.DAY_OF_MONTH, -90);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date threeMonthsAgo = calendar.getTime();

        // 查询所有收款记录
        LambdaQueryWrapper<BizContractMoneyEntity> queryWrapper = new LambdaQueryWrapper<>();
        List<BizContractMoneyEntity> allRecords = this.list(queryWrapper);

        // 初始化统计数据
        BigDecimal pendingAmount = BigDecimal.ZERO;                    // 待收款总金额
        BigDecimal yearPaidAmount = BigDecimal.ZERO;                   // 本年度已收款金额
        BigDecimal yearPendingAmount = BigDecimal.ZERO;                // 本年度待收款金额
        BigDecimal oneMonthPendingAmount = BigDecimal.ZERO;            // 最近一个月待收款金额
        BigDecimal threeMonthPendingAmount = BigDecimal.ZERO;          // 最近三个月待收款金额
        BigDecimal invoicedUnpaidAmount = BigDecimal.ZERO;             // 已开票未收款金额
        BigDecimal yearInvoicedAmount = BigDecimal.ZERO;               // 今年开票金额
        BigDecimal yearInvoicedUnpaidAmount = BigDecimal.ZERO;         // 今年已开票未收款金额

        int totalCount = allRecords.size();
        int paidCount = 0;
        int pendingCount = 0;
        int invoicedCount = 0;

        // 遍历所有记录进行统计
        for (BizContractMoneyEntity record : allRecords) {
            BigDecimal amount = record.getCmMoney() != null ? record.getCmMoney() : BigDecimal.ZERO;
            boolean isPaid = "1".equals(record.getPayStatus()) || "paid".equals(record.getPayStatus());
            boolean isInvoiced = record.getKaipiaoDate() != null;

            // 统计收款状态
            if (isPaid) {
                paidCount++;

                // 本年度已收款统计（按收款日期）
                if (record.getShoukuanDate() != null &&
                        record.getShoukuanDate().after(yearStart) &&
                        record.getShoukuanDate().before(yearEnd)) {
                    yearPaidAmount = yearPaidAmount.add(amount);
                }
            } else {
                pendingCount++;
                pendingAmount = pendingAmount.add(amount);

                // 本年度待收款统计（按预收日期）
                if (record.getYushouDate() != null &&
                        record.getYushouDate().after(yearStart) &&
                        record.getYushouDate().before(yearEnd)) {
                    yearPendingAmount = yearPendingAmount.add(amount);
                }

                // 最近一个月待收款统计（预收日期在最近30天内的未收款记录）
                if (record.getYushouDate() != null &&
                        !record.getYushouDate().before(oneMonthAgo) &&
                        !record.getYushouDate().after(now)) {
                    oneMonthPendingAmount = oneMonthPendingAmount.add(amount);
                }

                // 最近三个月待收款统计（预收日期在最近90天内的未收款记录）
                if (record.getYushouDate() != null &&
                        !record.getYushouDate().before(threeMonthsAgo) &&
                        !record.getYushouDate().after(now)) {
                    threeMonthPendingAmount = threeMonthPendingAmount.add(amount);
                }
            }

            // 开票相关统计
            if (isInvoiced) {
                invoicedCount++;

                // 今年开票金额统计
                if (record.getKaipiaoDate().after(yearStart) &&
                        record.getKaipiaoDate().before(yearEnd)) {
                    yearInvoicedAmount = yearInvoicedAmount.add(amount);

                    // 今年已开票未收款金额统计
                    if (!isPaid) {
                        yearInvoicedUnpaidAmount = yearInvoicedUnpaidAmount.add(amount);
                    }
                }

                // 已开票未收款金额统计（全部）
                if (!isPaid) {
                    invoicedUnpaidAmount = invoicedUnpaidAmount.add(amount);
                }
            }
        }

        // 添加调试日志
        System.out.println("统计时间范围:");
        System.out.println("当前时间: " + now);
        System.out.println("一个月前: " + oneMonthAgo);
        System.out.println("三个月前: " + threeMonthsAgo);
        System.out.println("本年开始: " + yearStart);
        System.out.println("本年结束: " + yearEnd);

        System.out.println("统计结果:");
        System.out.println("待收款总金额: " + pendingAmount);
        System.out.println("最近一个月待收款: " + oneMonthPendingAmount);
        System.out.println("最近三个月待收款: " + threeMonthPendingAmount);

        // 封装统计结果
        statistics.put("pendingAmount", pendingAmount);                        // 待收款总金额
        statistics.put("yearPaidAmount", yearPaidAmount);                      // 本年度已收款金额
        statistics.put("yearPendingAmount", yearPendingAmount);                // 本年度待收款金额
        statistics.put("oneMonthPendingAmount", oneMonthPendingAmount);        // 最近一个月待收款金额
        statistics.put("threeMonthPendingAmount", threeMonthPendingAmount);    // 最近三个月待收款金额
        statistics.put("invoicedUnpaidAmount", invoicedUnpaidAmount);          // 已开票未收款金额
        statistics.put("yearInvoicedAmount", yearInvoicedAmount);              // 今年开票金额
        statistics.put("yearInvoicedUnpaidAmount", yearInvoicedUnpaidAmount);  // 今年已开票未收款金额
        statistics.put("totalCount", totalCount);                             // 总记录数
        statistics.put("paidCount", paidCount);                               // 已收款记录数
        statistics.put("pendingCount", pendingCount);                         // 待收款记录数
        statistics.put("invoicedCount", invoicedCount);                       // 已开票记录数

        return statistics;
    }
}
