import { h } from 'vue';
import { Tag, Progress, Tooltip } from 'ant-design-vue';
import { useMessage } from '/@/hooks/web/useMessage';

/**
 * 模板管理通用工具函数
 */

// 状态相关工具
export const StatusUtils = {
  /**
   * 获取状态颜色
   */
  getStatusColor(status: string | number): string {
    if (typeof status === 'number') {
      return status === 1 ? 'success' : 'error';
    }
    
    const colorMap: Record<string, string> = {
      'draft': 'warning',
      'published': 'success',
      'archived': 'default',
      'enabled': 'success',
      'disabled': 'error',
      'active': 'processing',
      'inactive': 'default',
    };
    
    return colorMap[status] || 'default';
  },

  /**
   * 获取状态文本
   */
  getStatusText(status: string | number): string {
    if (typeof status === 'number') {
      return status === 1 ? '启用' : '禁用';
    }
    
    const textMap: Record<string, string> = {
      'draft': '未发布',
      'published': '已发布',
      'archived': '已归档',
      'enabled': '启用',
      'disabled': '禁用',
      'active': '活跃',
      'inactive': '非活跃',
    };
    
    return textMap[status] || status;
  },

  /**
   * 渲染状态标签
   */
  renderStatusTag(status: string | number, text?: string) {
    return h(Tag, {
      color: this.getStatusColor(status),
    }, () => text || this.getStatusText(status));
  },
};

// 布尔值相关工具
export const BooleanUtils = {
  /**
   * 获取布尔值文本
   */
  getBooleanText(value: number | boolean, trueText = '是', falseText = '否'): string {
    return (value === 1 || value === true) ? trueText : falseText;
  },

  /**
   * 获取布尔值颜色
   */
  getBooleanColor(value: number | boolean, trueColor = 'success', falseColor = 'default'): string {
    return (value === 1 || value === true) ? trueColor : falseColor;
  },

  /**
   * 渲染布尔值标签
   */
  renderBooleanTag(value: number | boolean, trueText = '是', falseText = '否') {
    const text = this.getBooleanText(value, trueText, falseText);
    const color = this.getBooleanColor(value);
    
    return h(Tag, { color }, () => text);
  },
};

// 数据格式化工具
export const FormatUtils = {
  /**
   * 格式化金额
   */
  formatAmount(amount: number | undefined, currency = '¥'): string {
    if (amount === undefined || amount === null) return '-';
    return `${currency}${amount.toLocaleString()}`;
  },

  /**
   * 格式化百分比
   */
  formatPercent(value: number, total: number, precision = 0): string {
    if (!total || total === 0) return '0%';
    const percent = Math.round((value / total) * 100 * Math.pow(10, precision)) / Math.pow(10, precision);
    return `${percent}%`;
  },

  /**
   * 格式化工期
   */
  formatDuration(days: number | undefined, unit = '天'): string {
    if (days === undefined || days === null) return '-';
    return `${days}${unit}`;
  },

  /**
   * 格式化文件大小
   */
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  },

  /**
   * 渲染进度条
   */
  renderProgress(current: number, total: number, showInfo = true) {
    if (!total || total === 0) return '-';
    const percent = Math.round((current / total) * 100);
    
    return h(Progress, {
      percent,
      size: 'small',
      showInfo,
      strokeColor: percent >= 100 ? '#52c41a' : percent >= 80 ? '#faad14' : '#1890ff',
    });
  },
};

// 验证工具
export const ValidationUtils = {
  /**
   * 验证编码格式
   */
  validateCode(code: string): boolean {
    // 编码格式：字母开头，可包含字母、数字、下划线，长度3-20
    const codeRegex = /^[A-Za-z][A-Za-z0-9_]{2,19}$/;
    return codeRegex.test(code);
  },

  /**
   * 验证名称格式
   */
  validateName(name: string): boolean {
    // 名称：不能为空，长度1-100，不能包含特殊字符
    const nameRegex = /^[A-Za-z0-9\u4e00-\u9fa5\s\-_()（）]{1,100}$/;
    return nameRegex.test(name);
  },

  /**
   * 验证工期
   */
  validateDuration(duration: number): boolean {
    return duration > 0 && duration <= 9999;
  },

  /**
   * 获取验证错误消息
   */
  getValidationMessage(field: string, type: 'required' | 'format' | 'range'): string {
    const messages = {
      required: `请输入${field}`,
      format: `${field}格式不正确`,
      range: `${field}超出有效范围`,
    };
    return messages[type];
  },
};

// 操作工具
export const ActionUtils = {
  /**
   * 确认删除操作
   */
  async confirmDelete(
    itemName: string,
    onConfirm: () => Promise<void>,
    count?: number
  ): Promise<void> {
    const { createConfirm } = useMessage();
    
    const title = count ? `批量删除确认` : `删除确认`;
    const content = count 
      ? `确定要删除选中的 ${count} 条记录吗？删除后无法恢复。`
      : `确定要删除"${itemName}"吗？删除后无法恢复。`;

    return new Promise((resolve, reject) => {
      createConfirm({
        iconType: 'warning',
        title,
        content,
        onOk: async () => {
          try {
            await onConfirm();
            resolve();
          } catch (error) {
            reject(error);
          }
        },
        onCancel: () => {
          reject(new Error('用户取消操作'));
        },
      });
    });
  },

  /**
   * 处理批量操作
   */
  async handleBatchAction(
    selectedRows: any[],
    actionName: string,
    action: (ids: string[]) => Promise<void>
  ): Promise<void> {
    const { createMessage } = useMessage();
    
    if (selectedRows.length === 0) {
      createMessage.warning(`请选择要${actionName}的记录`);
      return;
    }

    try {
      const ids = selectedRows.map(row => row.id);
      await action(ids);
      createMessage.success(`批量${actionName}成功`);
    } catch (error) {
      createMessage.error(`批量${actionName}失败`);
      throw error;
    }
  },

  /**
   * 处理单个操作
   */
  async handleSingleAction(
    record: any,
    actionName: string,
    action: (id: string) => Promise<void>
  ): Promise<void> {
    const { createMessage } = useMessage();
    
    try {
      await action(record.id);
      createMessage.success(`${actionName}成功`);
    } catch (error) {
      createMessage.error(`${actionName}失败`);
      throw error;
    }
  },
};

// 表格工具
export const TableUtils = {
  /**
   * 创建操作列配置
   */
  createActionColumn(width = 120): any {
    return {
      width,
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
    };
  },

  /**
   * 创建序号列配置
   */
  createIndexColumn(width = 60): any {
    return {
      title: '序号',
      dataIndex: 'index',
      width,
      customRender: ({ index }: { index: number }) => index + 1,
    };
  },

  /**
   * 创建状态列配置
   */
  createStatusColumn(dataIndex = 'status', title = '状态', width = 80): any {
    return {
      title,
      dataIndex,
      width,
      customRender: ({ record }: { record: any }) => {
        return StatusUtils.renderStatusTag(record[dataIndex]);
      },
    };
  },
};

// 导出所有工具
export default {
  StatusUtils,
  BooleanUtils,
  FormatUtils,
  ValidationUtils,
  ActionUtils,
  TableUtils,
};
