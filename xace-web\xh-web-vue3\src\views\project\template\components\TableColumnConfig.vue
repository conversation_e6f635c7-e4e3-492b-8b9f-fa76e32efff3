<template>
  <a-dropdown :trigger="['click']" placement="bottomRight">
    <a-button>
      <Icon icon="ant-design:setting-outlined" />
      列设置
    </a-button>
    
    <template #overlay>
      <div class="column-config-panel" @click.stop>
        <!-- 搜索 -->
        <div class="config-header">
          <a-input
            v-model:value="searchKeyword"
            placeholder="搜索列名"
            size="small"
            allow-clear
          >
            <template #prefix>
              <Icon icon="ant-design:search-outlined" />
            </template>
          </a-input>
        </div>

        <!-- 快捷操作 -->
        <div class="config-actions">
          <a-space size="small">
            <a-button type="link" size="small" @click="selectAll">
              全选
            </a-button>
            <a-button type="link" size="small" @click="selectNone">
              全不选
            </a-button>
            <a-button type="link" size="small" @click="resetColumns">
              重置
            </a-button>
            <a-button type="link" size="small" @click="saveAsTemplate">
              保存模板
            </a-button>
          </a-space>
        </div>

        <!-- 列配置列表 -->
        <div class="config-list">
          <VueDraggable
            v-model="configColumns"
            :animation="200"
            ghost-class="ghost"
            chosen-class="chosen"
            drag-class="drag"
            @end="handleDragEnd"
          >
            <div
              v-for="column in filteredColumns"
              :key="column.dataIndex"
              class="config-item"
              :class="{ disabled: column.fixed }"
            >
              <!-- 拖拽手柄 -->
              <Icon
                icon="ant-design:drag-outlined"
                class="drag-handle"
                :class="{ disabled: column.fixed }"
              />

              <!-- 显示/隐藏 -->
              <a-checkbox
                :checked="column.visible"
                :disabled="column.fixed"
                @change="(e) => toggleColumn(column.dataIndex, e.target.checked)"
              />

              <!-- 列名 -->
              <span class="column-title" :title="column.title">
                {{ column.title }}
              </span>

              <!-- 宽度调整 -->
              <div class="width-control" v-if="!column.fixed">
                <a-input-number
                  :value="column.width"
                  :min="50"
                  :max="500"
                  :step="10"
                  size="small"
                  @change="(value) => updateColumnWidth(column.dataIndex, value)"
                />
              </div>

              <!-- 固定设置 -->
              <a-dropdown :trigger="['click']" v-if="enableFixed">
                <a-button type="text" size="small">
                  <Icon icon="ant-design:pushpin-outlined" />
                </a-button>
                <template #overlay>
                  <a-menu @click="({ key }) => setColumnFixed(column.dataIndex, key)">
                    <a-menu-item key="">不固定</a-menu-item>
                    <a-menu-item key="left">固定左侧</a-menu-item>
                    <a-menu-item key="right">固定右侧</a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </div>
          </VueDraggable>
        </div>

        <!-- 底部操作 -->
        <div class="config-footer">
          <a-space>
            <a-button size="small" @click="applyConfig">
              应用
            </a-button>
            <a-button size="small" @click="cancelConfig">
              取消
            </a-button>
          </a-space>
        </div>
      </div>
    </template>
  </a-dropdown>

  <!-- 保存模板弹窗 -->
  <BasicModal
    @register="registerModal"
    title="保存列配置模板"
    width="400px"
    @ok="handleSaveTemplate"
  >
    <a-form layout="vertical">
      <a-form-item label="模板名称" required>
        <a-input
          v-model:value="templateName"
          placeholder="请输入模板名称"
          maxlength="50"
        />
      </a-form-item>
      <a-form-item label="模板描述">
        <a-textarea
          v-model:value="templateDescription"
          placeholder="请输入模板描述"
          :rows="3"
          maxlength="200"
        />
      </a-form-item>
      <a-form-item label="应用范围">
        <a-radio-group v-model:value="templateScope">
          <a-radio value="personal">个人</a-radio>
          <a-radio value="team">团队</a-radio>
          <a-radio value="global">全局</a-radio>
        </a-radio-group>
      </a-form-item>
    </a-form>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, computed, watch, onMounted } from 'vue';
  import { VueDraggable } from 'vue-draggable-plus';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { Icon } from '/@/components/Icon';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { cloneDeep } from 'lodash-es';

  interface Column {
    title: string;
    dataIndex: string;
    width?: number;
    fixed?: string | boolean;
    visible?: boolean;
    sortOrder?: number;
  }

  interface ColumnConfig extends Column {
    visible: boolean;
    sortOrder: number;
  }

  interface Props {
    columns: Column[];
    enableFixed?: boolean;
    enableWidth?: boolean;
    storageKey?: string;
  }

  const props = withDefaults(defineProps<Props>(), {
    enableFixed: true,
    enableWidth: true,
    storageKey: 'table-column-config',
  });

  const emit = defineEmits<{
    change: [columns: Column[]];
    saveTemplate: [config: any];
  }>();

  const { createMessage } = useMessage();

  // 状态
  const searchKeyword = ref('');
  const configColumns = ref<ColumnConfig[]>([]);
  const originalColumns = ref<ColumnConfig[]>([]);

  // 模板相关
  const templateName = ref('');
  const templateDescription = ref('');
  const templateScope = ref('personal');
  const [registerModal, { openModal, closeModal }] = useModalInner();

  // 计算属性
  const filteredColumns = computed(() => {
    if (!searchKeyword.value) {
      return configColumns.value;
    }
    return configColumns.value.filter(col =>
      col.title.toLowerCase().includes(searchKeyword.value.toLowerCase())
    );
  });

  // 初始化
  function initializeColumns() {
    configColumns.value = props.columns.map((col, index) => ({
      ...col,
      visible: col.visible !== false,
      sortOrder: col.sortOrder || index,
    }));
    originalColumns.value = cloneDeep(configColumns.value);
    loadSavedConfig();
  }

  // 方法
  function toggleColumn(dataIndex: string, visible: boolean) {
    const column = configColumns.value.find(col => col.dataIndex === dataIndex);
    if (column) {
      column.visible = visible;
    }
  }

  function updateColumnWidth(dataIndex: string, width: number) {
    const column = configColumns.value.find(col => col.dataIndex === dataIndex);
    if (column) {
      column.width = width;
    }
  }

  function setColumnFixed(dataIndex: string, fixed: string) {
    const column = configColumns.value.find(col => col.dataIndex === dataIndex);
    if (column) {
      column.fixed = fixed || undefined;
    }
  }

  function handleDragEnd() {
    // 更新排序
    configColumns.value.forEach((col, index) => {
      col.sortOrder = index;
    });
  }

  function selectAll() {
    configColumns.value.forEach(col => {
      if (!col.fixed) {
        col.visible = true;
      }
    });
  }

  function selectNone() {
    configColumns.value.forEach(col => {
      if (!col.fixed) {
        col.visible = false;
      }
    });
  }

  function resetColumns() {
    configColumns.value = cloneDeep(originalColumns.value);
  }

  function applyConfig() {
    const sortedColumns = [...configColumns.value]
      .sort((a, b) => a.sortOrder - b.sortOrder)
      .filter(col => col.visible);
    
    emit('change', sortedColumns);
    saveConfig();
    createMessage.success('列配置已应用');
  }

  function cancelConfig() {
    loadSavedConfig();
  }

  function saveAsTemplate() {
    templateName.value = '';
    templateDescription.value = '';
    templateScope.value = 'personal';
    openModal();
  }

  function handleSaveTemplate() {
    if (!templateName.value) {
      createMessage.warning('请输入模板名称');
      return;
    }

    const templateConfig = {
      name: templateName.value,
      description: templateDescription.value,
      scope: templateScope.value,
      columns: cloneDeep(configColumns.value),
      timestamp: Date.now(),
    };

    emit('saveTemplate', templateConfig);
    saveTemplateToStorage(templateConfig);
    createMessage.success('模板保存成功');
    closeModal();
  }

  function saveConfig() {
    try {
      const config = {
        columns: configColumns.value,
        timestamp: Date.now(),
      };
      localStorage.setItem(props.storageKey, JSON.stringify(config));
    } catch (error) {
      console.warn('保存列配置失败:', error);
    }
  }

  function loadSavedConfig() {
    try {
      const saved = localStorage.getItem(props.storageKey);
      if (saved) {
        const config = JSON.parse(saved);
        if (config.columns && Array.isArray(config.columns)) {
          // 合并保存的配置和当前列配置
          configColumns.value = mergeColumnConfig(configColumns.value, config.columns);
        }
      }
    } catch (error) {
      console.warn('加载列配置失败:', error);
    }
  }

  function mergeColumnConfig(current: ColumnConfig[], saved: ColumnConfig[]): ColumnConfig[] {
    const savedMap = new Map(saved.map(col => [col.dataIndex, col]));
    
    return current.map(col => {
      const savedCol = savedMap.get(col.dataIndex);
      if (savedCol) {
        return {
          ...col,
          visible: savedCol.visible,
          width: savedCol.width || col.width,
          fixed: savedCol.fixed || col.fixed,
          sortOrder: savedCol.sortOrder !== undefined ? savedCol.sortOrder : col.sortOrder,
        };
      }
      return col;
    });
  }

  function saveTemplateToStorage(template: any) {
    try {
      const templates = getTemplatesFromStorage();
      templates.push(template);
      localStorage.setItem(`${props.storageKey}-templates`, JSON.stringify(templates));
    } catch (error) {
      console.warn('保存模板失败:', error);
    }
  }

  function getTemplatesFromStorage(): any[] {
    try {
      const stored = localStorage.getItem(`${props.storageKey}-templates`);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.warn('获取模板失败:', error);
      return [];
    }
  }

  // 监听列变化
  watch(
    () => props.columns,
    () => {
      initializeColumns();
    },
    { immediate: true }
  );

  // 组件挂载时应用配置
  onMounted(() => {
    applyConfig();
  });
</script>

<style lang="less" scoped>
  .column-config-panel {
    width: 320px;
    max-height: 500px;
    background: white;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    overflow: hidden;

    .config-header {
      padding: 12px;
      border-bottom: 1px solid #f0f0f0;
    }

    .config-actions {
      padding: 8px 12px;
      border-bottom: 1px solid #f0f0f0;
      background: #fafafa;
    }

    .config-list {
      max-height: 300px;
      overflow-y: auto;
      padding: 8px 0;

      .config-item {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        gap: 8px;
        transition: background-color 0.2s;

        &:hover {
          background: #f5f5f5;
        }

        &.disabled {
          opacity: 0.6;
        }

        .drag-handle {
          cursor: move;
          color: #999;

          &.disabled {
            cursor: not-allowed;
          }
        }

        .column-title {
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          font-size: 12px;
        }

        .width-control {
          width: 80px;
        }
      }

      // 拖拽样式
      .ghost {
        opacity: 0.5;
      }

      .chosen {
        background: #e6f7ff;
      }

      .drag {
        background: #f0f0f0;
      }
    }

    .config-footer {
      padding: 12px;
      border-top: 1px solid #f0f0f0;
      background: #fafafa;
      text-align: right;
    }
  }

  // 滚动条样式
  .config-list {
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }
</style>
