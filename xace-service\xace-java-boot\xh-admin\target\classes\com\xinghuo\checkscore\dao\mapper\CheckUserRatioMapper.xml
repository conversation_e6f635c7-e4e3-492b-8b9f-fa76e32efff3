<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinghuo.checkscore.dao.CheckUserRatioMapper">
    <select id="isCheckRelation" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM zz_jx_check_user_relation_auto
        WHERE parent_user_id = #{userId} and user_id = #{parentUserId}
    </select>

    <select id="getLessScore" resultType="java.util.Map">
        SELECT user_name, ROUND(allScore - ownScore - xsScore, 2) AS diff
        FROM (SELECT c.user_id,
                     c.user_name,
                     SUM(c.person_act_score)                     AS ownScore,
                     SUM(c.check_dept_act_score)          AS allScore,
                     (SELECT SUM(d.check_dept_act_score)
                      FROM zz_jx_check_user_ratio d
                      WHERE d.parent_user_id = c.user_id) AS xsScore
              FROM zz_jx_check_user_ratio c
              WHERE c.user_id IN (SELECT DISTINCT d2.parent_user_id FROM zz_jx_check_user_ratio d2)
              GROUP BY user_id, user_name) s
        WHERE (s.ownScore + s.xsScore - s.allScore) >= 1
    </select>

    <select id="getNoPlatScore" resultType="java.util.Map">
        SELECT  user_name,ROUND(allScore - ownScore - xsScore, 2) score
        FROM  (SELECT
                    c.user_id, c.user_name,
                    SUM(c.person_act_score) AS ownScore,
                    SUM(c.check_dept_act_score) AS allScore,
                    (
                        SELECT SUM(d.check_dept_act_score)
                        FROM  zz_jx_check_user_ratio d
                        WHERE  d.parent_user_id = c.user_id
                    ) AS xsScore
                FROM  zz_jx_check_user_ratio c
                WHERE  c.user_id IN
                    ( SELECT  DISTINCT d2.parent_user_id
                        FROM  zz_jx_check_user_ratio d2
                    )
                GROUP BY  user_id,user_name
            ) AS s
        WHERE ABS(s.ownScore + s.xsScore - s.allScore) > 1
    </select>
</mapper>