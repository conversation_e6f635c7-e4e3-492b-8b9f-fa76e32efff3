package com.xinghuo.project.template.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.util.core.RandomUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.project.template.dao.WorkProductPlanTemplateDetailMapper;
import com.xinghuo.project.template.dao.WorkProductPlanTemplateMapper;
import com.xinghuo.project.template.entity.WorkProductPlanTemplateDetailEntity;
import com.xinghuo.project.template.entity.WorkProductPlanTemplateEntity;
import com.xinghuo.project.template.enums.TemplateRelationTypeEnum;
import com.xinghuo.project.template.model.WorkProductPlanTemplatePagination;
import com.xinghuo.project.template.model.dto.WorkProductPlanTemplateDTO;
import com.xinghuo.project.template.service.TemplateRelationService;
import com.xinghuo.project.template.service.WorkProductPlanTemplateService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 交付物计划模板服务实现类
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Slf4j
@Service
public class WorkProductPlanTemplateServiceImpl extends BaseServiceImpl<WorkProductPlanTemplateMapper, WorkProductPlanTemplateEntity> implements WorkProductPlanTemplateService {

    @Resource
    private WorkProductPlanTemplateMapper workProductPlanTemplateMapper;

    @Resource
    private WorkProductPlanTemplateDetailMapper workProductPlanTemplateDetailMapper;

    @Resource
    private TemplateRelationService templateRelationService;

    @Override
    public List<WorkProductPlanTemplateDTO> getList(WorkProductPlanTemplatePagination pagination) {
        QueryWrapper<WorkProductPlanTemplateEntity> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<WorkProductPlanTemplateEntity> lambda = queryWrapper.lambda();

        // 根据模板名称模糊查询
        if (StrXhUtil.isNotEmpty(pagination.getName())) {
            lambda.like(WorkProductPlanTemplateEntity::getName, pagination.getName());
        }

        // 根据知识状态精确查询
        if (StrXhUtil.isNotEmpty(pagination.getKnStatusId())) {
            lambda.eq(WorkProductPlanTemplateEntity::getKnStatusId, pagination.getKnStatusId());
        }

        // 创建时间范围查询
        if (pagination.getCreateTimeStart() != null) {
            lambda.ge(WorkProductPlanTemplateEntity::getCreatedAt, pagination.getCreateTimeStart());
        }
        if (pagination.getCreateTimeEnd() != null) {
            lambda.le(WorkProductPlanTemplateEntity::getCreatedAt, pagination.getCreateTimeEnd());
        }

        // 根据创建用户查询
//        if (StrXhUtil.isNotEmpty(pagination.getCreatorUserId())) {
//            lambda.eq(WorkProductPlanTemplateEntity::getCreatorUserId, pagination.getCreatorUserId());
//        }

        // 根据关键字搜索名称或描述
        String keyword = pagination.getKeyword();
        if (StrXhUtil.isNotEmpty(keyword)) {
            lambda.and(wrapper -> wrapper
                    .like(WorkProductPlanTemplateEntity::getName, keyword)
                    .or()
                    .like(WorkProductPlanTemplateEntity::getDescription, keyword)
            );
        }

        // 排序：按创建时间倒序
        lambda.orderByDesc(WorkProductPlanTemplateEntity::getCreatedAt);
        
        List<WorkProductPlanTemplateEntity> entities = processDataType(queryWrapper, pagination);
        
        // 转换为DTO并填充统计信息
        return entities.stream().map(this::convertToDTO).collect(Collectors.toList());
    }

    @Override
    public List<WorkProductPlanTemplateEntity> getListByKnStatus(String knStatusId) {
        return workProductPlanTemplateMapper.selectByKnStatus(knStatusId);
    }

    @Override
    public WorkProductPlanTemplateDTO getDetailInfo(String id) {
        if (StrXhUtil.isEmpty(id)) {
            log.warn("查询模板详情ID为空");
            return null;
        }
        return workProductPlanTemplateMapper.selectDetailById(id);
    }

    @Override
    public WorkProductPlanTemplateEntity getInfo(String id) {
        if (StrXhUtil.isEmpty(id)) {
            log.warn("查询模板信息ID为空");
            return null;
        }
        return this.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String create(WorkProductPlanTemplateDTO templateDTO) {
        if (templateDTO == null) {
            log.warn("创建模板信息为空");
            throw new RuntimeException("模板信息不能为空");
        }

        // 验证必填字段
        if (StrXhUtil.isEmpty(templateDTO.getName())) {
            log.warn("模板名称不能为空");
            throw new RuntimeException("模板名称不能为空");
        }

        // 检查模板名称是否重复
        boolean exists = isExistByName(templateDTO.getName(), null);
        if (exists) {
            log.warn("模板名称已存在: {}", templateDTO.getName());
            throw new RuntimeException("模板名称已存在");
        }

        // 创建主表记录
        String id = RandomUtil.snowId();
        templateDTO.setId(id);
        
        // 设置默认知识状态为"未发布"
        if (StrXhUtil.isEmpty(templateDTO.getKnStatusId())) {
            templateDTO.setKnStatusId("draft"); // 假设draft为未发布状态
        }

        this.save(templateDTO);

        // 保存交付物明细
        if (templateDTO.getWorkProductDetails() != null && !templateDTO.getWorkProductDetails().isEmpty()) {
            saveWorkProductDetails(id, templateDTO.getWorkProductDetails());
        }

        // 保存项目模板关联关系
        if (templateDTO.getProjectTemplateIds() != null && !templateDTO.getProjectTemplateIds().isEmpty()) {
            templateRelationService.updateTemplateRelations(id, TemplateRelationTypeEnum.WORKPRODUCT_TEMPLATE.getCode(),
                    templateDTO.getProjectTemplateIds(), TemplateRelationTypeEnum.PROJECT_TEMPLATE.getCode());
        }

        log.info("创建交付物计划模板成功, ID: {}, 名称: {}", id, templateDTO.getName());
        return id;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(String id, WorkProductPlanTemplateDTO templateDTO) {
        if (StrXhUtil.isEmpty(id) || templateDTO == null) {
            log.warn("更新模板参数无效, ID: {}", id);
            throw new RuntimeException("更新参数无效");
        }

        // 查询原记录是否存在
        WorkProductPlanTemplateEntity dbEntity = this.getById(id);
        if (dbEntity == null) {
            log.warn("更新的模板不存在, ID: {}", id);
            throw new RuntimeException("模板不存在");
        }

        // 验证必填字段
        if (StrXhUtil.isEmpty(templateDTO.getName())) {
            log.warn("模板名称不能为空");
            throw new RuntimeException("模板名称不能为空");
        }

        // 检查模板名称是否重复（排除自身）
        boolean exists = isExistByName(templateDTO.getName(), id);
        if (exists) {
            log.warn("模板名称已存在: {}", templateDTO.getName());
            throw new RuntimeException("模板名称已存在");
        }

        // 更新主表
        templateDTO.setId(id);
        this.updateById(templateDTO);

        // 删除原有交付物明细，重新保存
        workProductPlanTemplateDetailMapper.deleteByTemplateId(id);
        if (templateDTO.getWorkProductDetails() != null && !templateDTO.getWorkProductDetails().isEmpty()) {
            saveWorkProductDetails(id, templateDTO.getWorkProductDetails());
        }

        // 更新项目模板关联关系
        templateRelationService.updateTemplateRelations(id, TemplateRelationTypeEnum.WORKPRODUCT_TEMPLATE.getCode(),
                templateDTO.getProjectTemplateIds(), TemplateRelationTypeEnum.PROJECT_TEMPLATE.getCode());

        log.info("更新交付物计划模板成功, ID: {}, 名称: {}", id, templateDTO.getName());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        if (StrXhUtil.isEmpty(id)) {
            log.warn("删除模板ID为空");
            throw new RuntimeException("模板ID不能为空");
        }

        // 查询模板是否存在
        WorkProductPlanTemplateEntity entity = this.getById(id);
        if (entity == null) {
            log.warn("删除的模板不存在, ID: {}", id);
            throw new RuntimeException("模板不存在");
        }

        // 检查模板是否正在使用
        Map<String, Object> usageInfo = getTemplateUsageInfo(id);
        Integer usageCount = (Integer) usageInfo.get("usageCount");
        if (usageCount != null && usageCount > 0) {
            log.warn("模板正在使用中，不能删除, ID: {}", id);
            throw new RuntimeException("模板正在使用中，不能删除");
        }

        // 删除关联数据
        workProductPlanTemplateDetailMapper.deleteByTemplateId(id);
        templateRelationService.deleteBySourceTemplate(id, TemplateRelationTypeEnum.WORKPRODUCT_TEMPLATE.getCode());
        
        // 删除主表
        this.removeById(id);
        log.info("删除交付物计划模板成功, ID: {}, 名称: {}", id, entity.getName());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDelete(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            log.warn("批量删除模板ID列表为空");
            throw new RuntimeException("ID列表不能为空");
        }

        for (String id : ids) {
            delete(id);
        }
        log.info("批量删除交付物计划模板成功, 数量: {}", ids.size());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateKnStatus(String id, String knStatusId) {
        if (StrXhUtil.isEmpty(id) || StrXhUtil.isEmpty(knStatusId)) {
            log.warn("更新模板知识状态参数无效, ID: {}, knStatusId: {}", id, knStatusId);
            throw new RuntimeException("参数无效");
        }

        workProductPlanTemplateMapper.updateKnStatus(id, knStatusId);
        log.info("更新模板知识状态成功, ID: {}, knStatusId: {}", id, knStatusId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateKnStatus(List<String> ids, String knStatusId) {
        if (ids == null || ids.isEmpty() || StrXhUtil.isEmpty(knStatusId)) {
            log.warn("批量更新模板知识状态参数无效");
            throw new RuntimeException("参数无效");
        }

        workProductPlanTemplateMapper.batchUpdateKnStatus(ids, knStatusId);
        log.info("批量更新模板知识状态成功, 数量: {}, knStatusId: {}", ids.size(), knStatusId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void publish(String id) {
        updateKnStatus(id, "published"); // 假设published为已发布状态
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void archive(String id) {
        updateKnStatus(id, "archived"); // 假设archived为已归档状态
    }

    @Override
    public boolean isExistByName(String name, String excludeId) {
        if (StrXhUtil.isEmpty(name)) {
            return false;
        }

        QueryWrapper<WorkProductPlanTemplateEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("name", name);

        // 如果有排除ID，则添加排除条件
        if (StrXhUtil.isNotEmpty(excludeId)) {
            queryWrapper.ne("id", excludeId);
        }

        long count = this.count(queryWrapper);
        return count > 0;
    }

    @Override
    public List<WorkProductPlanTemplateDTO> getSelectList(String keyword, String knStatusId) {
        QueryWrapper<WorkProductPlanTemplateEntity> queryWrapper = new QueryWrapper<>();

        // 添加关键字搜索条件
        if (StrXhUtil.isNotEmpty(keyword)) {
            queryWrapper.like("name", keyword);
        }

        // 添加知识状态筛选条件
        if (StrXhUtil.isNotEmpty(knStatusId)) {
            queryWrapper.eq("kn_status_id", knStatusId);
        }

        // 按名称排序
        queryWrapper.orderByAsc("name");

        List<WorkProductPlanTemplateEntity> entities = this.list(queryWrapper);

        // 转换为DTO
        List<WorkProductPlanTemplateDTO> dtoList = new ArrayList<>();
        for (WorkProductPlanTemplateEntity entity : entities) {
            WorkProductPlanTemplateDTO dto = new WorkProductPlanTemplateDTO();
            dto.setId(entity.getId());
            dto.setName(entity.getName());
            dto.setDescription(entity.getDescription());
            dto.setKnStatusId(entity.getKnStatusId());
            dtoList.add(dto);
        }

        return dtoList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String copy(String id, String newName) {
        if (StrXhUtil.isEmpty(id) || StrXhUtil.isEmpty(newName)) {
            log.warn("复制模板参数无效, ID: {}, newName: {}", id, newName);
            throw new RuntimeException("参数无效");
        }

        // 查询原模板详情
        WorkProductPlanTemplateDTO sourceTemplate = getDetailInfo(id);
        if (sourceTemplate == null) {
            log.warn("复制的模板不存在, ID: {}", id);
            throw new RuntimeException("模板不存在");
        }

        // 创建新模板
        WorkProductPlanTemplateDTO newTemplate = new WorkProductPlanTemplateDTO();
        newTemplate.setName(newName);
        newTemplate.setDescription(sourceTemplate.getDescription());
        newTemplate.setKnStatusId("draft"); // 复制的模板默认为草稿状态
        newTemplate.setWorkProductDetails(sourceTemplate.getWorkProductDetails());
        newTemplate.setProjectTemplateIds(sourceTemplate.getProjectTemplateIds());

        String newId = create(newTemplate);
        log.info("复制交付物计划模板成功, 源ID: {}, 新ID: {}, 新名称: {}", id, newId, newName);
        return newId;
    }

    @Override
    public List<WorkProductPlanTemplateDetailEntity> addWorkProductsFromLibrary(String templateId, List<String> workProductLibraryIds) {
        // 这里需要实现从标准交付物库添加交付物的逻辑
        // 暂时返回空列表，具体实现需要根据标准交付物库的设计
        return new ArrayList<>();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateWorkProductOrder(String templateId, List<Map<String, Object>> workProductOrders) {
        if (StrXhUtil.isEmpty(templateId) || workProductOrders == null || workProductOrders.isEmpty()) {
            log.warn("更新交付物顺序参数无效");
            throw new RuntimeException("参数无效");
        }

        List<WorkProductPlanTemplateDetailEntity> details = new ArrayList<>();
        for (Map<String, Object> order : workProductOrders) {
            WorkProductPlanTemplateDetailEntity detail = new WorkProductPlanTemplateDetailEntity();
            detail.setId((String) order.get("id"));
            detail.setSeqNo((Integer) order.get("seqNo"));
            details.add(detail);
        }

        workProductPlanTemplateDetailMapper.batchUpdateSeqNo(details);
        log.info("更新交付物顺序成功, 模板ID: {}", templateId);
    }

    @Override
    public List<Map<String, Object>> getTemplateStatistics(Map<String, Object> params) {
        return workProductPlanTemplateMapper.getTemplateStatistics(params);
    }

    @Override
    public List<WorkProductPlanTemplateEntity> getByProjectTemplateId(String projectTplId) {
        // 先通过通用关联表查询关联的交付物计划模板ID列表
        List<String> templateIds = templateRelationService.getSourceTemplateIds(projectTplId, 
                TemplateRelationTypeEnum.PROJECT_TEMPLATE.getCode(), 
                TemplateRelationTypeEnum.WORKPRODUCT_TEMPLATE.getCode());
        
        if (templateIds.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 根据ID列表查询模板详情
        return this.listByIds(templateIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProjectTemplateRelations(String templateId, List<String> projectTemplateIds) {
        if (StrXhUtil.isEmpty(templateId)) {
            log.warn("更新项目模板关联关系，模板ID为空");
            throw new RuntimeException("模板ID不能为空");
        }

        // 更新项目模板关联关系
        templateRelationService.updateTemplateRelations(templateId, TemplateRelationTypeEnum.WORKPRODUCT_TEMPLATE.getCode(),
                projectTemplateIds, TemplateRelationTypeEnum.PROJECT_TEMPLATE.getCode());

        log.info("更新项目模板关联关系成功, 模板ID: {}", templateId);
    }

    @Override
    public String createFromProject(String projectId, String templateName, String description) {
        // 这里需要实现从项目创建模板的逻辑
        // 暂时抛出异常，具体实现需要根据项目管理模块的设计
        throw new RuntimeException("从项目创建模板功能待实现");
    }

    @Override
    public Map<String, Object> applyToProject(String templateId, String projectId) {
        // 这里需要实现应用模板到项目的逻辑
        // 暂时抛出异常，具体实现需要根据项目管理模块的设计
        throw new RuntimeException("应用模板到项目功能待实现");
    }

    @Override
    public Map<String, Object> getTemplateUsageInfo(String id) {
        return workProductPlanTemplateMapper.getTemplateUsageInfo(id);
    }

    @Override
    public List<WorkProductPlanTemplateDetailEntity> getWorkProductsByStageTemplate(String stageTemplateId) {
        return workProductPlanTemplateDetailMapper.selectByStageTemplateId(stageTemplateId);
    }

    @Override
    public List<WorkProductPlanTemplateDetailEntity> getWorkProductsByActivityTemplate(String activityTemplateId) {
        return workProductPlanTemplateDetailMapper.selectByActivityTemplateId(activityTemplateId);
    }

    /**
     * 转换实体为DTO
     */
    private WorkProductPlanTemplateDTO convertToDTO(WorkProductPlanTemplateEntity entity) {
        WorkProductPlanTemplateDTO dto = new WorkProductPlanTemplateDTO();
        dto.setId(entity.getId());
        dto.setName(entity.getName());
        dto.setDescription(entity.getDescription());
        dto.setKnStatusId(entity.getKnStatusId());
        dto.setCreatedAt(entity.getCreatedAt());
//        dto.setCreatorUserId(entity.getCreatorUserId());
        
        // 查询交付物明细统计信息
        List<WorkProductPlanTemplateDetailEntity> details = workProductPlanTemplateDetailMapper.selectByTemplateId(entity.getId());
        dto.setWorkProductCount(details.size());
        dto.setReviewRequiredCount((int) details.stream().filter(d -> d.getNeedReview() != null && d.getNeedReview() == 1).count());
        dto.setDeliverableCount((int) details.stream().filter(d -> d.getIsDeliverable() != null && d.getIsDeliverable() == 1).count());
        
        return dto;
    }

    /**
     * 保存交付物明细
     */
    private void saveWorkProductDetails(String templateId, List<WorkProductPlanTemplateDetailEntity> workProductDetails) {
        for (int i = 0; i < workProductDetails.size(); i++) {
            WorkProductPlanTemplateDetailEntity detail = workProductDetails.get(i);
            detail.setId(RandomUtil.snowId());
            detail.setWorkproductPlanTplId(templateId);
            detail.setSeqNo(i + 1);
            if (detail.getNeedReview() == null) {
                detail.setNeedReview(0);
            }
            if (detail.getIsDeliverable() == null) {
                detail.setIsDeliverable(0);
            }
            if (detail.getCanCut() == null) {
                detail.setCanCut(1);
            }
        }
        workProductPlanTemplateDetailMapper.batchInsert(workProductDetails);
    }
}
