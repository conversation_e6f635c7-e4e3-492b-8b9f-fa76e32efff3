management:
  health:
    mail:
      enabled: false
  server:
    servlet:
      context-path: /actuator
#  endpoints:
#    web:
#      exposure:
#        include: '*'
#  endpoint:
#    health:
#      show-details: always
#    # 开启在线日志查看功能
#    logfile:
#      enabled: true
server:
  port: 30020
  servlet:
    context-path: /xxl-job-admin
spring:
  profiles:
    active: vpn
  main:
    allow-bean-definition-overriding: true
  application:
    name: xxl-job-register
  freemarker:
    charset: UTF-8
    request-context-attribute: request
    settings:
      number_format: 0.##########
    suffix: .ftl
    templateLoaderPath: classpath:/templates/
  mail:
    from: <EMAIL>
    host: smtp.qq.com
    password: xxx
    port: 25
    properties:
      mail:
        smtp:
          auth: true
          socketFactory:
            class: javax.net.ssl.SSLSocketFactory
          starttls:
            enable: true
            required: true
    username: <EMAIL>
  mvc:
    servlet:
      load-on-startup: 0
    static-path-pattern: /static/**
  resources:
    static-locations: classpath:/static/
xxl:
  job:
    accessToken: ''
    i18n: zh_CN
    logretentiondays: 30
    triggerpool:
      fast:
        max: 200
      slow:
        max: 100
logging:
  level:
    org.apache.ibatis: DEBUG
    com.xxl.job.admin.dao: DEBUG