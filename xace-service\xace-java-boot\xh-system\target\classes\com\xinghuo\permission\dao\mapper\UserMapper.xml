<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinghuo.permission.dao.UserMapper">

    <select id="getListId" resultType="java.lang.String">
        SELECT F_Id from base_user WHERE F_EnabledMark = 1
    </select>

    <select id="query" resultType="java.lang.String">
        SELECT F_Id FROM
        (SELECT DISTINCT u.F_Id,u.F_SortCode,u.F_CreatorTime
        FROM
        <if test="dbSchema!=null">
            #{dbSchema}.base_userrelation ul1,#{dbSchema}.base_user u
        </if>
        <if test="dbSchema==null">
            base_userrelation ul1,base_user u
        </if>
        WHERE u.F_Id = ul1.F_UserId
        AND u.F_Account != 'admin'
        <if test="account != null and account != ''">
            and (u.F_Account like #{account} or u.F_RealName like #{account} or u.F_MobilePhone like
            #{account})
        </if>
        <if test="orgIdList != null and orgIdList.size > 0">
            AND ul1.F_ObjectId IN
            <trim suffixOverrides=" OR ul1.F_ObjectId IN()">
                <foreach collection="orgIdList" item="orgIds" index="index" open="(" close=")">
                    <if test="index != 0">
                        <choose>
                            <when test="index % 1000 == 999">) OR ul1.F_ObjectId IN (</when>
                            <otherwise>,</otherwise>
                        </choose>
                    </if>
                    #{orgIds}
                </foreach>
            </trim>
        </if>
        ORDER BY u.F_SortCode ASC,u.F_CreatorTime DESC) uu
    </select>

    <select id="getMaxLastModifyTime" resultType="java.lang.String">
        SELECT MAX(F_LASTMODIFYTIME) FROM BASE_USER
    </select>

</mapper>
