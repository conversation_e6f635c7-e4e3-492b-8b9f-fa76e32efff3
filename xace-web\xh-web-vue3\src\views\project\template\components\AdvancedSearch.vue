<template>
  <div class="advanced-search">
    <!-- 快速搜索 -->
    <div class="quick-search">
      <a-input-search
        v-model:value="quickSearchValue"
        :placeholder="quickSearchPlaceholder"
        :loading="searching"
        allow-clear
        enter-button
        @search="handleQuickSearch"
        @change="handleQuickSearchChange"
        class="quick-search-input"
      />
      <a-button
        type="link"
        @click="toggleAdvanced"
        class="advanced-toggle"
      >
        {{ showAdvanced ? '收起' : '高级搜索' }}
        <Icon :icon="showAdvanced ? 'ant-design:up-outlined' : 'ant-design:down-outlined'" />
      </a-button>
    </div>

    <!-- 高级搜索 -->
    <div v-show="showAdvanced" class="advanced-search-panel">
      <BasicForm @register="registerForm" />
      
      <!-- 搜索历史 -->
      <div v-if="searchHistory.length > 0" class="search-history">
        <div class="history-title">
          <span>搜索历史</span>
          <a-button type="link" size="small" @click="clearHistory">
            清空
          </a-button>
        </div>
        <div class="history-tags">
          <a-tag
            v-for="(item, index) in searchHistory"
            :key="index"
            closable
            @click="applyHistorySearch(item)"
            @close="removeHistoryItem(index)"
            class="history-tag"
          >
            {{ formatHistoryItem(item) }}
          </a-tag>
        </div>
      </div>

      <!-- 快捷筛选 -->
      <div v-if="quickFilters.length > 0" class="quick-filters">
        <div class="filters-title">快捷筛选</div>
        <div class="filters-content">
          <a-tag
            v-for="filter in quickFilters"
            :key="filter.key"
            :color="activeQuickFilters.includes(filter.key) ? 'blue' : 'default'"
            checkable
            :checked="activeQuickFilters.includes(filter.key)"
            @change="toggleQuickFilter(filter.key)"
            class="filter-tag"
          >
            {{ filter.label }}
          </a-tag>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="search-actions">
        <a-space>
          <a-button type="primary" @click="handleAdvancedSearch" :loading="searching">
            搜索
          </a-button>
          <a-button @click="handleReset">
            重置
          </a-button>
          <a-button type="link" @click="saveAsTemplate" v-if="canSaveTemplate">
            保存为模板
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 当前搜索条件 -->
    <div v-if="hasActiveFilters" class="active-filters">
      <div class="filters-title">当前筛选条件：</div>
      <div class="filters-content">
        <a-tag
          v-for="filter in activeFilters"
          :key="filter.key"
          closable
          @close="removeFilter(filter.key)"
          color="blue"
        >
          {{ filter.label }}: {{ filter.value }}
        </a-tag>
        <a-button type="link" size="small" @click="clearAllFilters">
          清空所有
        </a-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed, watch, onMounted } from 'vue';
  import { BasicForm, useForm, FormSchema } from '/@/components/Form/index';
  import { Icon } from '/@/components/Icon';
  import { debounce } from 'lodash-es';

  interface QuickFilter {
    key: string;
    label: string;
    value: any;
  }

  interface SearchHistory {
    keyword?: string;
    filters?: Record<string, any>;
    timestamp: number;
  }

  interface Props {
    schemas: FormSchema[];
    quickSearchPlaceholder?: string;
    quickFilters?: QuickFilter[];
    enableHistory?: boolean;
    enableQuickFilters?: boolean;
    canSaveTemplate?: boolean;
    maxHistoryCount?: number;
  }

  const props = withDefaults(defineProps<Props>(), {
    quickSearchPlaceholder: '请输入关键字搜索',
    quickFilters: () => [],
    enableHistory: true,
    enableQuickFilters: true,
    canSaveTemplate: false,
    maxHistoryCount: 10,
  });

  const emit = defineEmits<{
    search: [params: Record<string, any>];
    quickSearch: [keyword: string];
    reset: [];
    saveTemplate: [params: Record<string, any>];
  }>();

  // 状态
  const quickSearchValue = ref('');
  const showAdvanced = ref(false);
  const searching = ref(false);
  const searchHistory = ref<SearchHistory[]>([]);
  const activeQuickFilters = ref<string[]>([]);

  // 表单
  const [registerForm, { getFieldsValue, setFieldsValue, resetFields }] = useForm({
    labelWidth: 100,
    baseColProps: { span: 8 },
    schemas: props.schemas,
    showActionButtonGroup: false,
    autoSubmitOnEnter: true,
  });

  // 计算属性
  const activeFilters = computed(() => {
    const formValues = getFieldsValue();
    const filters: Array<{ key: string; label: string; value: string }> = [];
    
    // 添加表单筛选条件
    Object.entries(formValues).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        const schema = props.schemas.find(s => s.field === key);
        const label = schema?.label || key;
        filters.push({
          key,
          label,
          value: Array.isArray(value) ? value.join(', ') : String(value),
        });
      }
    });

    // 添加快捷筛选条件
    activeQuickFilters.value.forEach(filterKey => {
      const filter = props.quickFilters.find(f => f.key === filterKey);
      if (filter) {
        filters.push({
          key: filterKey,
          label: filter.label,
          value: '已选择',
        });
      }
    });

    return filters;
  });

  const hasActiveFilters = computed(() => {
    return activeFilters.value.length > 0 || quickSearchValue.value;
  });

  // 防抖的快速搜索
  const debouncedQuickSearch = debounce((keyword: string) => {
    emit('quickSearch', keyword);
  }, 300);

  // 方法
  function toggleAdvanced() {
    showAdvanced.value = !showAdvanced.value;
  }

  function handleQuickSearch(keyword: string) {
    quickSearchValue.value = keyword;
    addToHistory({ keyword, timestamp: Date.now() });
    emit('quickSearch', keyword);
  }

  function handleQuickSearchChange() {
    if (quickSearchValue.value) {
      debouncedQuickSearch(quickSearchValue.value);
    }
  }

  function handleAdvancedSearch() {
    const formValues = getFieldsValue();
    const searchParams = {
      keyword: quickSearchValue.value,
      ...formValues,
      quickFilters: activeQuickFilters.value,
    };
    
    addToHistory({
      keyword: quickSearchValue.value,
      filters: formValues,
      timestamp: Date.now(),
    });
    
    emit('search', searchParams);
  }

  function handleReset() {
    quickSearchValue.value = '';
    activeQuickFilters.value = [];
    resetFields();
    emit('reset');
  }

  function toggleQuickFilter(filterKey: string) {
    const index = activeQuickFilters.value.indexOf(filterKey);
    if (index > -1) {
      activeQuickFilters.value.splice(index, 1);
    } else {
      activeQuickFilters.value.push(filterKey);
    }
  }

  function removeFilter(filterKey: string) {
    // 移除表单筛选
    const formValues = getFieldsValue();
    if (filterKey in formValues) {
      setFieldsValue({ [filterKey]: undefined });
    }
    
    // 移除快捷筛选
    const quickFilterIndex = activeQuickFilters.value.indexOf(filterKey);
    if (quickFilterIndex > -1) {
      activeQuickFilters.value.splice(quickFilterIndex, 1);
    }
  }

  function clearAllFilters() {
    quickSearchValue.value = '';
    activeQuickFilters.value = [];
    resetFields();
  }

  function addToHistory(item: SearchHistory) {
    if (!props.enableHistory) return;
    
    // 避免重复添加
    const exists = searchHistory.value.some(h => 
      h.keyword === item.keyword && 
      JSON.stringify(h.filters) === JSON.stringify(item.filters)
    );
    
    if (!exists) {
      searchHistory.value.unshift(item);
      // 限制历史记录数量
      if (searchHistory.value.length > props.maxHistoryCount) {
        searchHistory.value = searchHistory.value.slice(0, props.maxHistoryCount);
      }
      saveHistoryToStorage();
    }
  }

  function applyHistorySearch(item: SearchHistory) {
    if (item.keyword) {
      quickSearchValue.value = item.keyword;
    }
    if (item.filters) {
      setFieldsValue(item.filters);
    }
    handleAdvancedSearch();
  }

  function removeHistoryItem(index: number) {
    searchHistory.value.splice(index, 1);
    saveHistoryToStorage();
  }

  function clearHistory() {
    searchHistory.value = [];
    saveHistoryToStorage();
  }

  function formatHistoryItem(item: SearchHistory): string {
    const parts: string[] = [];
    if (item.keyword) {
      parts.push(item.keyword);
    }
    if (item.filters && Object.keys(item.filters).length > 0) {
      parts.push('高级筛选');
    }
    return parts.join(' + ') || '搜索记录';
  }

  function saveAsTemplate() {
    const formValues = getFieldsValue();
    const templateData = {
      keyword: quickSearchValue.value,
      ...formValues,
      quickFilters: activeQuickFilters.value,
    };
    emit('saveTemplate', templateData);
  }

  function saveHistoryToStorage() {
    try {
      localStorage.setItem('template-search-history', JSON.stringify(searchHistory.value));
    } catch (error) {
      console.warn('保存搜索历史失败:', error);
    }
  }

  function loadHistoryFromStorage() {
    try {
      const stored = localStorage.getItem('template-search-history');
      if (stored) {
        searchHistory.value = JSON.parse(stored);
      }
    } catch (error) {
      console.warn('加载搜索历史失败:', error);
    }
  }

  // 生命周期
  onMounted(() => {
    if (props.enableHistory) {
      loadHistoryFromStorage();
    }
  });

  // 监听快捷筛选变化
  watch(activeQuickFilters, () => {
    if (activeQuickFilters.value.length > 0) {
      handleAdvancedSearch();
    }
  }, { deep: true });
</script>

<style lang="less" scoped>
  .advanced-search {
    .quick-search {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 16px;

      &-input {
        flex: 1;
      }

      .advanced-toggle {
        white-space: nowrap;
      }
    }

    &-panel {
      background: #fafafa;
      border-radius: 6px;
      padding: 16px;
      margin-bottom: 16px;
    }

    .search-history,
    .quick-filters {
      margin-bottom: 16px;

      .history-title,
      .filters-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 8px;
        font-weight: 500;
        color: #666;
      }

      .history-tags,
      .filters-content {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
      }

      .history-tag,
      .filter-tag {
        cursor: pointer;
        transition: all 0.2s;

        &:hover {
          transform: translateY(-1px);
        }
      }
    }

    .search-actions {
      text-align: right;
    }

    .active-filters {
      background: #e6f7ff;
      border: 1px solid #91d5ff;
      border-radius: 6px;
      padding: 12px;
      margin-bottom: 16px;

      .filters-title {
        font-weight: 500;
        color: #1890ff;
        margin-bottom: 8px;
      }

      .filters-content {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        align-items: center;
      }
    }
  }
</style>
