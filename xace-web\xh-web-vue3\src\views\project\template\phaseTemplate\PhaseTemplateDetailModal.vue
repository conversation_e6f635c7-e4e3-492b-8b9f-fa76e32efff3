<template>
  <BasicModal 
    v-bind="$attrs" 
    @register="registerModal" 
    title="阶段模板详情" 
    :footer="null"
    width="1000px"
  >
    <div class="phase-template-detail">
      <!-- 基本信息 -->
      <div class="detail-section">
        <h3 class="section-title">基本信息</h3>
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="阶段编码">{{ phaseInfo.code }}</a-descriptions-item>
          <a-descriptions-item label="阶段名称">{{ phaseInfo.name }}</a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-tag :color="getStatusColor(phaseInfo.status)">
              {{ getStatusText(phaseInfo.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="标准工期">{{ phaseInfo.stdDuration }} 天</a-descriptions-item>
          <a-descriptions-item label="阶段描述" :span="2">{{ phaseInfo.description || '暂无描述' }}</a-descriptions-item>
        </a-descriptions>
      </div>

      <!-- 配置信息 -->
      <div class="detail-section">
        <h3 class="section-title">配置信息</h3>
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="默认审批流程">
            {{ phaseInfo.defaultApprovalName || '未配置' }}
          </a-descriptions-item>
          <a-descriptions-item label="默认检查单模板">
            {{ phaseInfo.defaultChecklistName || '未配置' }}
          </a-descriptions-item>
        </a-descriptions>
      </div>

      <!-- 使用统计 -->
      <div class="detail-section">
        <h3 class="section-title">使用统计</h3>
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="使用次数">{{ phaseInfo.usageCount || 0 }}</a-descriptions-item>
          <a-descriptions-item label="关联项目模板数量">{{ phaseInfo.projectTemplateCount || 0 }}</a-descriptions-item>
        </a-descriptions>
      </div>

      <!-- 创建信息 -->
      <div class="detail-section">
        <h3 class="section-title">创建信息</h3>
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="创建时间">{{ phaseInfo.createdAt }}</a-descriptions-item>
          <a-descriptions-item label="创建人">{{ phaseInfo.createdByName }}</a-descriptions-item>
          <a-descriptions-item label="最后修改时间">{{ phaseInfo.lastUpdatedAt }}</a-descriptions-item>
          <a-descriptions-item label="最后修改人">{{ phaseInfo.lastUpdatedByName }}</a-descriptions-item>
        </a-descriptions>
      </div>

      <!-- 关联活动模板 -->
      <div class="detail-section" v-if="activityTemplates.length > 0">
        <h3 class="section-title">关联活动模板</h3>
        <a-table 
          :columns="activityColumns" 
          :data-source="activityTemplates" 
          :pagination="false"
          size="small"
          bordered
        />
      </div>

      <!-- 关联交付物 -->
      <div class="detail-section" v-if="workProducts.length > 0">
        <h3 class="section-title">关联交付物</h3>
        <a-table 
          :columns="workProductColumns" 
          :data-source="workProducts" 
          :pagination="false"
          size="small"
          bordered
        />
      </div>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { getPhaseTemplateInfo } from '/@/api/project/phaseTemplate';
  import { Descriptions } from 'ant-design-vue';
  const ADescriptions = Descriptions;
  const ADescriptionsItem = Descriptions.Item;

  defineOptions({ name: 'PhaseTemplateDetailModal' });

  const phaseInfo = ref<any>({});
  const activityTemplates = ref<any[]>([]);
  const workProducts = ref<any[]>([]);

  // 活动模板表格列
  const activityColumns = [
    { title: '活动编码', dataIndex: 'code', width: 120 },
    { title: '活动名称', dataIndex: 'name', width: 200 },
    { title: '标准工期(天)', dataIndex: 'stdDuration', width: 100 },
    { title: '状态', dataIndex: 'statusName', width: 80 },
    { title: '创建时间', dataIndex: 'createdAt', width: 180 },
  ];

  // 交付物表格列
  const workProductColumns = [
    { title: '交付物编码', dataIndex: 'code', width: 120 },
    { title: '交付物名称', dataIndex: 'name', width: 200 },
    { title: '交付物类型', dataIndex: 'typeName', width: 120 },
    { title: '责任角色', dataIndex: 'responsibleRoleName', width: 120 },
    { title: '需要评审', dataIndex: 'needReviewText', width: 80 },
    { title: '最终交付成果', dataIndex: 'isDeliverableText', width: 100 },
  ];

  const [registerModal, { setModalProps }] = useModalInner(async (data) => {
    setModalProps({ confirmLoading: true });
    
    try {
      if (data?.record) {
        const result = await getPhaseTemplateInfo(data.record.id);
        const detailData = result.data || result;
        
        phaseInfo.value = detailData;
        activityTemplates.value = detailData.activityTemplates || [];
        workProducts.value = detailData.workProducts || [];
      }
    } catch (error) {
      console.error('获取阶段模板详情失败:', error);
    } finally {
      setModalProps({ confirmLoading: false });
    }
  });

  function getStatusColor(status: number) {
    return status === 1 ? 'success' : 'error';
  }

  function getStatusText(status: number) {
    return status === 1 ? '启用' : '禁用';
  }
</script>

<style lang="less" scoped>
  .phase-template-detail {
    .detail-section {
      margin-bottom: 24px;
      
      .section-title {
        margin-bottom: 16px;
        font-size: 16px;
        font-weight: 600;
        color: #262626;
        border-left: 4px solid #1890ff;
        padding-left: 12px;
      }
    }
  }
</style>
