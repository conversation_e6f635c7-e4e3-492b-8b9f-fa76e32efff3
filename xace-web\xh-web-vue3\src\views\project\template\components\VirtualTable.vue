<template>
  <div class="virtual-table" :style="{ height: `${height}px` }">
    <!-- 表头 -->
    <div class="virtual-table-header" ref="headerRef">
      <div class="virtual-table-row header-row">
        <div
          v-for="column in columns"
          :key="column.dataIndex"
          class="virtual-table-cell header-cell"
          :style="{ width: `${column.width || 100}px` }"
        >
          {{ column.title }}
        </div>
      </div>
    </div>

    <!-- 虚拟滚动容器 -->
    <div
      class="virtual-table-body"
      ref="containerRef"
      @scroll="handleScroll"
      :style="{ height: `${height - headerHeight}px` }"
    >
      <!-- 占位元素，用于撑开滚动条 -->
      <div :style="{ height: `${totalHeight}px`, position: 'relative' }">
        <!-- 可见行 -->
        <div
          v-for="(item, index) in visibleData"
          :key="getRowKey(item, startIndex + index)"
          class="virtual-table-row"
          :style="{
            position: 'absolute',
            top: `${(startIndex + index) * itemHeight}px`,
            width: '100%',
          }"
        >
          <div
            v-for="column in columns"
            :key="column.dataIndex"
            class="virtual-table-cell"
            :style="{ width: `${column.width || 100}px` }"
          >
            <!-- 自定义渲染 -->
            <template v-if="column.customRender">
              <component
                :is="column.customRender({ record: item, index: startIndex + index, column })"
              />
            </template>
            <!-- 默认渲染 -->
            <template v-else>
              {{ getFieldValue(item, column.dataIndex) }}
            </template>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="virtual-table-loading">
      <a-spin size="large" />
    </div>

    <!-- 空状态 -->
    <div v-if="!loading && data.length === 0" class="virtual-table-empty">
      <a-empty :description="emptyText" />
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue';
  import { debounce } from 'lodash-es';

  interface Column {
    title: string;
    dataIndex: string;
    width?: number;
    customRender?: (params: { record: any; index: number; column: Column }) => any;
  }

  interface Props {
    data: any[];
    columns: Column[];
    height?: number;
    itemHeight?: number;
    rowKey?: string | ((record: any) => string);
    loading?: boolean;
    emptyText?: string;
    overscan?: number; // 预渲染的行数
  }

  const props = withDefaults(defineProps<Props>(), {
    height: 400,
    itemHeight: 54,
    rowKey: 'id',
    loading: false,
    emptyText: '暂无数据',
    overscan: 5,
  });

  const emit = defineEmits<{
    scroll: [event: Event];
    rowClick: [record: any, index: number];
  }>();

  // 引用
  const containerRef = ref<HTMLElement>();
  const headerRef = ref<HTMLElement>();

  // 状态
  const scrollTop = ref(0);
  const headerHeight = ref(54);

  // 计算属性
  const totalHeight = computed(() => props.data.length * props.itemHeight);

  const visibleCount = computed(() => {
    return Math.ceil((props.height - headerHeight.value) / props.itemHeight) + props.overscan * 2;
  });

  const startIndex = computed(() => {
    const index = Math.floor(scrollTop.value / props.itemHeight) - props.overscan;
    return Math.max(0, index);
  });

  const endIndex = computed(() => {
    const index = startIndex.value + visibleCount.value;
    return Math.min(props.data.length, index);
  });

  const visibleData = computed(() => {
    return props.data.slice(startIndex.value, endIndex.value);
  });

  // 防抖的滚动处理
  const handleScroll = debounce((event: Event) => {
    const target = event.target as HTMLElement;
    scrollTop.value = target.scrollTop;
    emit('scroll', event);
  }, 16); // 约60fps

  // 获取行键
  function getRowKey(record: any, index: number): string {
    if (typeof props.rowKey === 'function') {
      return props.rowKey(record);
    }
    return record[props.rowKey] || index.toString();
  }

  // 获取字段值
  function getFieldValue(record: any, dataIndex: string): any {
    const keys = dataIndex.split('.');
    let value = record;
    for (const key of keys) {
      if (value && typeof value === 'object' && key in value) {
        value = value[key];
      } else {
        return '';
      }
    }
    return value;
  }

  // 滚动到指定位置
  function scrollTo(index: number) {
    if (containerRef.value) {
      const targetScrollTop = index * props.itemHeight;
      containerRef.value.scrollTop = targetScrollTop;
      scrollTop.value = targetScrollTop;
    }
  }

  // 滚动到顶部
  function scrollToTop() {
    scrollTo(0);
  }

  // 滚动到底部
  function scrollToBottom() {
    if (containerRef.value) {
      containerRef.value.scrollTop = totalHeight.value;
      scrollTop.value = totalHeight.value;
    }
  }

  // 获取当前可见范围
  function getVisibleRange() {
    return {
      start: startIndex.value,
      end: endIndex.value,
    };
  }

  // 监听数据变化，重置滚动位置
  watch(
    () => props.data.length,
    () => {
      nextTick(() => {
        if (containerRef.value) {
          containerRef.value.scrollTop = 0;
          scrollTop.value = 0;
        }
      });
    }
  );

  // 组件挂载时获取表头高度
  onMounted(() => {
    nextTick(() => {
      if (headerRef.value) {
        headerHeight.value = headerRef.value.offsetHeight;
      }
    });
  });

  // 暴露方法
  defineExpose({
    scrollTo,
    scrollToTop,
    scrollToBottom,
    getVisibleRange,
  });
</script>

<style lang="less" scoped>
  .virtual-table {
    position: relative;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    overflow: hidden;

    &-header {
      background: #fafafa;
      border-bottom: 1px solid #f0f0f0;
      position: sticky;
      top: 0;
      z-index: 10;
    }

    &-body {
      overflow: auto;
      position: relative;
    }

    &-row {
      display: flex;
      border-bottom: 1px solid #f0f0f0;
      transition: background-color 0.2s;

      &:hover {
        background-color: #f5f5f5;
      }

      &.header-row {
        font-weight: 600;
        background: #fafafa;

        &:hover {
          background: #fafafa;
        }
      }
    }

    &-cell {
      padding: 12px 16px;
      border-right: 1px solid #f0f0f0;
      display: flex;
      align-items: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      &:last-child {
        border-right: none;
      }

      &.header-cell {
        background: #fafafa;
        font-weight: 600;
      }
    }

    &-loading {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 20;
    }

    &-empty {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 20;
    }
  }

  // 滚动条样式
  .virtual-table-body {
    &::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 4px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }
</style>
