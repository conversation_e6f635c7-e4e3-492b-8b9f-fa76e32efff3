<template>
  <BasicModal v-bind="$attrs" @register="registerModal" title="应用模板到项目" @ok="handleSubmit">
    <div class="apply-to-project">
      <!-- 模板信息 -->
      <div class="template-info">
        <h4>模板信息</h4>
        <a-descriptions :column="2" bordered size="small">
          <a-descriptions-item label="模板名称">{{ templateInfo.name }}</a-descriptions-item>
          <a-descriptions-item label="模板类型">{{ templateType }}</a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-tag :color="getStatusColor(templateInfo.status || templateInfo.knStatusId)">
              {{ getStatusText(templateInfo.status || templateInfo.knStatusId) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="描述" :span="2">{{ templateInfo.description || '暂无描述' }}</a-descriptions-item>
        </a-descriptions>
      </div>

      <!-- 项目选择 -->
      <div class="project-selection">
        <h4>选择目标项目</h4>
        <BasicForm @register="registerForm" />
      </div>

      <!-- 应用选项 -->
      <div class="apply-options">
        <h4>应用选项</h4>
        <a-checkbox-group v-model:value="selectedOptions">
          <a-row>
            <a-col :span="24">
              <a-checkbox value="overwrite">覆盖现有配置</a-checkbox>
            </a-col>
            <a-col :span="24">
              <a-checkbox value="backup">应用前备份现有配置</a-checkbox>
            </a-col>
            <a-col :span="24">
              <a-checkbox value="notify">应用完成后通知项目成员</a-checkbox>
            </a-col>
          </a-row>
        </a-checkbox-group>
      </div>

      <!-- 预览信息 -->
      <div class="preview-info" v-if="selectedProject">
        <h4>应用预览</h4>
        <a-alert 
          :message="`将向项目 '${selectedProject.projectName}' 应用模板 '${templateInfo.name}'`"
          type="info" 
          show-icon 
        />
        <div class="preview-details" v-if="previewData">
          <a-descriptions :column="2" bordered size="small" style="margin-top: 12px;">
            <a-descriptions-item label="将添加阶段数">{{ previewData.phaseCount || 0 }}</a-descriptions-item>
            <a-descriptions-item label="将添加活动数">{{ previewData.activityCount || 0 }}</a-descriptions-item>
            <a-descriptions-item label="将添加交付物数">{{ previewData.workProductCount || 0 }}</a-descriptions-item>
            <a-descriptions-item label="预计总工期">{{ previewData.totalDuration || 0 }} 天</a-descriptions-item>
          </a-descriptions>
        </div>
      </div>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, computed, watch } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm, FormSchema } from '/@/components/Form/index';

  defineOptions({ name: 'ApplyToProjectModal' });

  const emit = defineEmits(['success', 'register']);

  const templateInfo = ref<any>({});
  const templateType = ref('');
  const selectedOptions = ref<string[]>(['backup', 'notify']);
  const selectedProject = ref<any>(null);
  const previewData = ref<any>(null);

  const formSchema: FormSchema[] = [
    {
      field: 'projectId',
      label: '目标项目',
      component: 'ApiSelect',
      required: true,
      componentProps: {
        api: () => {
          // 这里应该调用获取项目列表的API
          return Promise.resolve([
            { label: '示例项目1', value: 'project1', projectName: '示例项目1' },
            { label: '示例项目2', value: 'project2', projectName: '示例项目2' },
          ]);
        },
        labelField: 'label',
        valueField: 'value',
        placeholder: '请选择要应用模板的项目',
        onChange: (value: string, option: any) => {
          selectedProject.value = option;
          loadPreviewData();
        },
      },
    },
    {
      field: 'remark',
      label: '应用说明',
      component: 'InputTextArea',
      componentProps: {
        placeholder: '请输入应用说明（可选）',
        rows: 3,
        maxlength: 500,
        showCount: true,
      },
    },
  ];

  const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
    labelWidth: 100,
    baseColProps: { span: 24 },
    schemas: formSchema,
    showActionButtonGroup: false,
    autoSubmitOnEnter: true,
  });

  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    resetFields();
    setModalProps({ confirmLoading: false });
    selectedOptions.value = ['backup', 'notify'];
    selectedProject.value = null;
    previewData.value = null;

    if (data?.record) {
      templateInfo.value = data.record;
      
      // 根据不同的模板类型设置不同的标题和类型
      if (data.templateType) {
        templateType.value = data.templateType;
      } else {
        // 根据字段判断模板类型
        if (templateInfo.value.knStatusId !== undefined) {
          templateType.value = templateInfo.value.phases ? '阶段计划模板' : '交付物计划模板';
        } else {
          templateType.value = templateInfo.value.code ? '阶段模板' : '标准交付物';
        }
      }
    }
  });

  async function loadPreviewData() {
    if (!selectedProject.value || !templateInfo.value.id) return;
    
    try {
      // 这里应该调用API获取预览数据
      // const result = await getApplyPreviewData(templateInfo.value.id, selectedProject.value.value);
      // previewData.value = result.data;
      
      // 模拟预览数据
      previewData.value = {
        phaseCount: 5,
        activityCount: 15,
        workProductCount: 20,
        totalDuration: 120,
      };
    } catch (error) {
      console.error('获取预览数据失败:', error);
    }
  }

  async function handleSubmit() {
    try {
      const values = await validate();
      setModalProps({ confirmLoading: true });

      const applyData = {
        templateId: templateInfo.value.id,
        projectId: values.projectId,
        options: selectedOptions.value,
        remark: values.remark,
      };

      // 这里应该调用应用模板的API
      // await applyTemplateToProject(applyData);
      
      console.log('应用模板数据:', applyData);
      
      closeModal();
      emit('success');
    } catch (error) {
      console.error('应用模板失败:', error);
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }

  function getStatusColor(status: any) {
    if (typeof status === 'number') {
      return status === 1 ? 'success' : 'error';
    }
    const colorMap = {
      'draft': 'warning',
      'published': 'success',
      'archived': 'default',
      'enabled': 'success',
      'disabled': 'error',
    };
    return colorMap[status] || 'default';
  }

  function getStatusText(status: any) {
    if (typeof status === 'number') {
      return status === 1 ? '启用' : '禁用';
    }
    const textMap = {
      'draft': '未发布',
      'published': '已发布',
      'archived': '已归档',
      'enabled': '启用',
      'disabled': '禁用',
    };
    return textMap[status] || status;
  }
</script>

<style lang="less" scoped>
  .apply-to-project {
    .template-info,
    .project-selection,
    .apply-options,
    .preview-info {
      margin-bottom: 24px;
      
      h4 {
        margin-bottom: 12px;
        font-size: 14px;
        font-weight: 600;
        color: #262626;
      }
    }

    .preview-details {
      margin-top: 12px;
    }
  }
</style>
