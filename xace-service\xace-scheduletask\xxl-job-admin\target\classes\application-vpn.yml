spring:
  # 数据源
#  datasource:
#    url: ***************************************************************************************************************************************************
#    username: root
#    password: My2sq3l_*paSs
#    driver-class-name: com.mysql.cj.jdbc.Driver
#    db-type: MySQL

  # ===================== 数据源配置 =====================
  exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure #排除自动配置，手动配置druid
  datasource:
    db-type: MySQL #数据库类型(可选值 MySQL、SQLServer、Oracle、DM8、KingbaseES、PostgreSQL，请严格按可选值填写)
    host: **************
    port: 18830
    username: root
    password: My2sq3l_*paSs
    db-name: xace200_xxljob
    db-schema: #金仓达梦选填
    prepare-url: #自定义url
  data:
    redis:
      database: 1 #缓存库编号
      host: 127.0.0.1
      port: 8001
      password: RedisXHGig123@pass    # 密码为空时，请将本行注释
      timeout: 3000 #超时时间(单位：秒)
      lettuce: #Lettuce为Redis的Java驱动包
        pool:
          max-active: 50 # 连接池最大连接数
          max-wait: 6000  # 连接池最大阻塞等待时间（使用负值表示没有限制）
          min-idle: 10 # 连接池中的最小空闲连接
          max-idle: 50 # 连接池中的最大空闲连接
#mybatis-plus:
#  type-aliases-package: com.xinghuo.**.entity;com.xxl.job.admin.core.model
#  mapper-locations: classpath*:com/xxl/job/admin/dao/mapper/*Mapper.xml
