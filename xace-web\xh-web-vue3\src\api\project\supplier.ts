import { defHttp } from '/@/utils/http/axios';
import { ListResult } from '/@/api/model/baseModel';

/**
 * 供应商管理API
 */

// API URL前缀 - 更新为新的架构路径
const API_PREFIX = '/api/project/biz/supplier';

/**
 * 供应商对象接口
 */
export interface SupplierModel {
  id: string;
  name: string;
  linkman: string;
  telephone: string;
  remark: string;
  sortCode: number;
  // 新的字段名称，匹配后端返回数据
  createdAt?: number | string;
  createdBy?: string;
  lastUpdatedAt?: number | string;
  lastUpdatedBy?: string;
  // 保留旧字段名称以兼容
  creatorUserId: string;
  creatorUserName?: string;
  creatorTime: string;
  lastModifyUserId: string;
  lastModifyUserName?: string;
  lastModifyTime: string;
  deleteMark: number;
}

/**
 * 供应商表单接口
 */
export interface SupplierFormModel {
  name: string;
  linkman?: string;
  telephone?: string;
  remark?: string;
  sortCode?: number;
}

/**
 * 供应商查询参数接口
 */
export interface SupplierQueryParams {
  name?: string;
  linkman?: string;
  telephone?: string;
  keyword?: string;
  // 分页参数 - 继承自 Pagination
  pageSize?: number;
  currentPage?: number;
  sort?: string;
  sidx?: string;
}

/**
 * 获取供应商列表
 * @param params 查询参数
 * @returns 供应商列表
 */
export const getSupplierList = (params?: SupplierQueryParams) => {
  console.log('供应商API请求参数:', params);
  return defHttp
    .post<ListResult<SupplierModel>>({
      url: API_PREFIX + '/getList',
      data: params,
    })
    .then(result => {
      console.log('供应商API返回结果:', result);
      return result;
    });
};

/**
 * 获取供应商详情
 * @param id 供应商ID
 * @returns 供应商详情
 */
export const getSupplierInfo = (id: string) => {
  console.log('获取供应商详情API请求参数:', id);
  return defHttp
    .get<SupplierModel>({
      url: `${API_PREFIX}/${id}`,
    })
    .then(result => {
      console.log('供应商详情API返回结果:', result);
      return result;
    });
};

/**
 * 创建供应商
 * @param params 供应商创建参数
 * @returns 操作结果
 */
export const createSupplier = (params: SupplierFormModel) => {
  return defHttp.post<void>({
    url: `${API_PREFIX}`,
    data: params,
  });
};

/**
 * 更新供应商
 * @param id 供应商ID
 * @param params 供应商更新参数
 * @returns 操作结果
 */
export const updateSupplier = (id: string, params: SupplierFormModel) => {
  return defHttp.put<void>({
    url: `${API_PREFIX}/${id}`,
    data: params,
  });
};

/**
 * 删除供应商
 * @param id 供应商ID
 * @returns 操作结果
 */
export const deleteSupplier = (id: string) => {
  return defHttp.delete<void>({
    url: `${API_PREFIX}/${id}`,
  });
};

/**
 * 检查供应商名称是否已存在
 * @param name 供应商名称
 * @param id 供应商ID（更新时使用，新增时为null）
 * @returns 是否存在
 */
export const checkSupplierName = (name: string, id?: string) => {
  return defHttp.get<boolean>({
    url: `${API_PREFIX}/checkNameExists`,
    params: {
      name,
      excludeId: id,
    },
  });
};

/**
 * 供应商选择器选项接口
 */
export interface SupplierSelectorOption {
  id: string;
  fullName: string;
  name: string;
  code?: string;
}

/**
 * 供应商选择器响应接口
 */
export interface SupplierSelectorResponse {
  code: number;
  msg: string;
  data: SupplierSelectorOption[];
}

/**
 * 获取供应商选择器选项
 * @param keyword 搜索关键字
 * @returns 供应商选择器选项列表
 */
export const getSupplierSelector = (keyword?: string) => {
  return defHttp.get<SupplierSelectorResponse>({
    url: `${API_PREFIX}/selector`,
    params: {
      keyword,
    },
  });
};

/**
 * 获取供应商关联的采购合同列表
 * @param supplierId 供应商ID
 * @param params 查询参数
 * @returns 采购合同列表
 */
export const getSupplierPaycontracts = (supplierId: string, params?: any) => {
  return defHttp.post({
    url: `${API_PREFIX}/${supplierId}/paycontracts`,
    data: params,
  });
};
