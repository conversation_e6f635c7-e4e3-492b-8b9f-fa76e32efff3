{"groups": [{"name": "gateway", "type": "com.xinghuo.admin.util.GatewayWhite", "sourceType": "com.xinghuo.admin.util.GatewayWhite"}], "properties": [{"name": "gateway.block-url", "type": "java.util.List<java.lang.String>", "description": "禁止访问", "sourceType": "com.xinghuo.admin.util.GatewayWhite"}, {"name": "gateway.exclude-url", "type": "java.util.List<java.lang.String>", "description": "放行不记录", "sourceType": "com.xinghuo.admin.util.GatewayWhite"}, {"name": "gateway.white-ip", "type": "java.util.List<java.lang.String>", "description": "禁止访问地址的白名单IP startsWith匹配, 访问IP.startsWith(whiteIP)", "sourceType": "com.xinghuo.admin.util.GatewayWhite"}, {"name": "gateway.white-url", "type": "java.util.List<java.lang.String>", "description": "不验证Token, 记录访问", "sourceType": "com.xinghuo.admin.util.GatewayWhite"}], "hints": []}