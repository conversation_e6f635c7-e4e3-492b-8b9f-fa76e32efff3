<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinghuo.project.core.dao.ProjectBaseMapper">

    <!-- 项目基础信息结果映射 -->
    <resultMap id="BaseResultMap" type="com.xinghuo.project.core.entity.ProjectBaseEntity">
        <id column="f_id" property="id" jdbcType="VARCHAR"/>
        <result column="code" property="code" jdbcType="VARCHAR"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="type_id" property="typeId" jdbcType="VARCHAR"/>
        <result column="manager_id" property="managerId" jdbcType="VARCHAR"/>
        <result column="program_id" property="programId" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="health" property="health" jdbcType="VARCHAR"/>
        <result column="planned_start_date" property="plannedStartDate" jdbcType="TIMESTAMP"/>
        <result column="planned_end_date" property="plannedEndDate" jdbcType="TIMESTAMP"/>
        <result column="actual_start_date" property="actualStartDate" jdbcType="TIMESTAMP"/>
        <result column="actual_end_date" property="actualEndDate" jdbcType="TIMESTAMP"/>
        <result column="priority" property="priority" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="department_id" property="departmentId" jdbcType="VARCHAR"/>
        <result column="budget_amount" property="budgetAmount" jdbcType="DECIMAL"/>
        <result column="actual_cost" property="actualCost" jdbcType="DECIMAL"/>
        <result column="customer_id" property="customerId" jdbcType="VARCHAR"/>
        <result column="contract_id" property="contractId" jdbcType="VARCHAR"/>
        <result column="f_created_at" property="createdAt" jdbcType="TIMESTAMP"/>
        <result column="f_updated_at" property="updatedAt" jdbcType="TIMESTAMP"/>
        <result column="f_created_by" property="createdBy" jdbcType="VARCHAR"/>
        <result column="f_updated_by" property="updatedBy" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 项目简要信息结果映射 -->
    <resultMap id="SimpleProjectInfoResultMap" type="com.xinghuo.project.core.model.dto.SimpleProjectInfoDTO">
        <id column="f_id" property="id" jdbcType="VARCHAR"/>
        <result column="code" property="code" jdbcType="VARCHAR"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="status_name" property="statusName" jdbcType="VARCHAR"/>
        <result column="manager_id" property="managerUserId" jdbcType="VARCHAR"/>
        <result column="manager_name" property="managerUserName" jdbcType="VARCHAR"/>
        <result column="department_id" property="deptId" jdbcType="VARCHAR"/>
        <result column="department_name" property="deptName" jdbcType="VARCHAR"/>
        <result column="program_id" property="refGroup" jdbcType="VARCHAR"/>
        <result column="program_name" property="refGroupName" jdbcType="VARCHAR"/>
        <result column="planned_end_date" property="expectEndTime" jdbcType="TIMESTAMP"/>
        <result column="f_created_at" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="health" property="healthStatus" jdbcType="INTEGER"/>
        <result column="health_name" property="healthStatusName" jdbcType="VARCHAR"/>
        <result column="priority" property="priority" jdbcType="INTEGER"/>
        <result column="priority_name" property="priorityName" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 项目扩展信息结果映射 -->
    <resultMap id="ExtendedProjectInfoResultMap" type="com.xinghuo.project.core.model.dto.ProjectExtendedDTO" extends="BaseResultMap">
        <result column="status_name" property="statusName" jdbcType="VARCHAR"/>
        <result column="type_name" property="typeName" jdbcType="VARCHAR"/>
        <result column="manager_name" property="managerName" jdbcType="VARCHAR"/>
        <result column="department_name" property="departmentName" jdbcType="VARCHAR"/>
        <result column="program_name" property="programName" jdbcType="VARCHAR"/>
        <result column="customer_name" property="customerName" jdbcType="VARCHAR"/>
        <result column="contract_name" property="contractName" jdbcType="VARCHAR"/>
        <result column="health_name" property="healthName" jdbcType="VARCHAR"/>
        <result column="priority_name" property="priorityName" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础SQL片段 -->
    <sql id="Base_Column_List">
        p.f_id, p.code, p.name, p.type_id, p.manager_id, p.program_id, p.status, p.health,
        p.planned_start_date, p.planned_end_date, p.actual_start_date, p.actual_end_date,
        p.priority, p.description, p.department_id, p.budget_amount, p.actual_cost,
        p.customer_id, p.contract_id, p.f_created_at, p.f_updated_at, p.f_created_by, p.f_updated_by
    </sql>

    <!-- 根据项目类型查询项目列表 -->
    <select id="selectByProjectType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM zz_proj_base p
        WHERE p.type_id = #{projectType}
        ORDER BY p.f_created_at DESC
    </select>

    <!-- 根据项目状态查询项目列表 -->
    <select id="selectByStatus" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM zz_proj_base p
        WHERE p.status = #{status}
        ORDER BY p.f_created_at DESC
    </select>

    <!-- 根据项目经理ID查询项目列表 -->
    <select id="selectByManagerId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM zz_proj_base p
        WHERE p.manager_id = #{managerId}
        ORDER BY p.f_created_at DESC
    </select>

    <!-- 根据部门ID查询项目列表 -->
    <select id="selectByDeptId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM zz_proj_base p
        WHERE p.department_id = #{deptId}
        ORDER BY p.f_created_at DESC
    </select>

    <!-- 根据项目群ID查询项目列表 -->
    <select id="selectByProgramId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM zz_proj_base p
        WHERE p.program_id = #{programId}
        ORDER BY p.f_created_at DESC
    </select>

    <!-- 根据客户ID查询项目列表 -->
    <select id="selectByCustomerId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM zz_proj_base p
        WHERE p.customer_id = #{customerId}
        ORDER BY p.f_created_at DESC
    </select>

    <!-- 根据合同ID查询项目列表 -->
    <select id="selectByContractId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM zz_proj_base p
        WHERE p.contract_id = #{contractId}
        ORDER BY p.f_created_at DESC
    </select>

    <!-- 检查项目编码是否存在 -->
    <select id="checkCodeExists" resultType="int">
        SELECT COUNT(1)
        FROM zz_proj_base
        WHERE code = #{code}
        <if test="excludeId != null and excludeId != ''">
            AND f_id != #{excludeId}
        </if>
    </select>

    <!-- 获取项目简要信息 -->
    <select id="selectSimpleProjectInfo" resultMap="SimpleProjectInfoResultMap">
        SELECT 
            p.f_id, p.code, p.name, p.status, p.manager_id, p.department_id, p.program_id,
            p.planned_end_date, p.f_created_at, p.health, p.priority,
            CASE p.status 
                WHEN 'planning' THEN '规划中'
                WHEN 'executing' THEN '进行中'
                WHEN 'completed' THEN '已完成'
                WHEN 'archived' THEN '已归档'
                ELSE '未知'
            END as status_name,
            u.f_name as manager_name,
            d.f_name as department_name,
            pg.f_name as program_name,
            CASE p.health 
                WHEN 'normal' THEN '正常'
                WHEN 'warning' THEN '预警'
                WHEN 'risk' THEN '风险'
                ELSE '未知'
            END as health_name,
            CASE p.priority 
                WHEN 'high' THEN '高'
                WHEN 'medium' THEN '中'
                WHEN 'low' THEN '低'
                ELSE '未知'
            END as priority_name
        FROM zz_proj_base p
        LEFT JOIN sys_user u ON p.manager_id = u.f_id
        LEFT JOIN sys_department d ON p.department_id = d.f_id
        LEFT JOIN zz_proj_program pg ON p.program_id = pg.f_id
        WHERE p.f_id = #{projectId}
    </select>

    <!-- 获取项目扩展信息 -->
    <select id="selectExtendedProjectInfo" resultMap="ExtendedProjectInfoResultMap">
        SELECT 
            <include refid="Base_Column_List"/>,
            CASE p.status 
                WHEN 'planning' THEN '规划中'
                WHEN 'executing' THEN '进行中'
                WHEN 'completed' THEN '已完成'
                WHEN 'archived' THEN '已归档'
                ELSE '未知'
            END as status_name,
            pt.f_name as type_name,
            u.f_name as manager_name,
            d.f_name as department_name,
            pg.f_name as program_name,
            c.f_name as customer_name,
            ct.f_name as contract_name,
            CASE p.health 
                WHEN 'normal' THEN '正常'
                WHEN 'warning' THEN '预警'
                WHEN 'risk' THEN '风险'
                ELSE '未知'
            END as health_name,
            CASE p.priority 
                WHEN 'high' THEN '高'
                WHEN 'medium' THEN '中'
                WHEN 'low' THEN '低'
                ELSE '未知'
            END as priority_name
        FROM zz_proj_base p
        LEFT JOIN sys_user u ON p.manager_id = u.f_id
        LEFT JOIN sys_department d ON p.department_id = d.f_id
        LEFT JOIN zz_proj_program pg ON p.program_id = pg.f_id
        LEFT JOIN zz_proj_customer c ON p.customer_id = c.f_id
        LEFT JOIN zz_proj_contract ct ON p.contract_id = ct.f_id
        LEFT JOIN sys_dict_item pt ON p.type_id = pt.f_id
        WHERE p.f_id = #{projectId}
    </select>

    <!-- 更新项目状态 -->
    <update id="updateStatus">
        UPDATE zz_proj_base 
        SET status = #{status}, f_updated_at = NOW()
        WHERE f_id = #{id}
    </update>

    <!-- 更新项目健康度 -->
    <update id="updateHealth">
        UPDATE zz_proj_base 
        SET health = #{health}, f_updated_at = NOW()
        WHERE f_id = #{id}
    </update>

    <!-- 批量更新项目状态 -->
    <update id="batchUpdateStatus">
        UPDATE zz_proj_base 
        SET status = #{status}, f_updated_at = NOW()
        WHERE f_id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 根据关键字搜索项目 -->
    <select id="searchByKeyword" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM zz_proj_base p
        WHERE (p.name LIKE CONCAT('%', #{keyword}, '%') 
               OR p.code LIKE CONCAT('%', #{keyword}, '%'))
        ORDER BY p.f_created_at DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

</mapper>
