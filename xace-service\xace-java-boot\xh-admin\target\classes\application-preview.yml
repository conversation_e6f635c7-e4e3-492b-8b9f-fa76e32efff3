# 应用服务器
server:
  tomcat:
    uri-encoding: UTF-8 #tomcat编码
  port: 30000 #tomcat端口
  forward-headers-strategy: framework #处理ngx+swagger3访问swagger-config找不到路径的问题

spring:
  devtools: #spring开发者工具模块
    restart:
      enabled: true #热部署开关
    freemarker:
      cache: false #spring内置freemarker缓存
  thymeleaf:
    cache: false #spring内置thymeleaf缓存

  # ===================== 数据源配置 =====================
  exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure #排除自动配置，手动配置druid
  datasource:
    db-type: MySQL #数据库类型(可选值 MySQL、SQLServer、Oracle、DM8、KingbaseES、PostgreSQL，请严格按可选值填写)
    host: *************
    port: 3306
    username: java_boot_test
    password: pBx5HaW6WMGSTdDf
    db-name: java_boot_test
    db-schema: #金仓达梦选填
    prepare-url: #自定义url

    # ===================== 动态多数据源 =====================
    dynamic:
      primary: master #设置默认的数据源或者数据源组,默认值即为master
      strict: true #严格匹配数据源,默认false. true未匹配到指定数据源时抛异常,false使用默认数据源
      druid:
        #定时执行数据链接检测语句 防止数据库闲时超时断开链接
        test-while-idle: true #空闲时执行
        time-between-eviction-runs-millis: 60000 #执行间隔
  #      datasource:
  #        master:
  #          url: jdbc:mysql://${spring.datasource.host}:${spring.datasource.port}/${spring.datasource.dbname}?useUnicode=true&characterEncoding=utf-8&useSSL=false&allowMultiQueries=true&serverTimezone=UTC
  #          username: ${spring.datasource.username}
  #          password: ${spring.datasource.password}
  #          driver-class-name: com.mysql.cj.jdbc.Driver

  # ===================== Redis配置 =====================
  # redis单机模式
  redis:
    database: 1 #缓存库编号
    host: *************
    port: 6379
    password: 123456  # 密码为空时，请将本行注释
    timeout: 3000 #超时时间(单位：秒)
    lettuce: #Lettuce为Redis的Java驱动包
      pool:
        max-active: 8 # 连接池最大连接数
        max-wait: -1ms  # 连接池最大阻塞等待时间（使用负值表示没有限制）
        min-idle: 0 # 连接池中的最小空闲连接
        max-idle: 8 # 连接池中的最大空闲连接

# redis集群模式
#  redis:
#    cluster:
#      nodes:
#        - *************:6380
#        - *************:6381
#        - *************:6382
#        - *************:6383
#        - *************:6384
#        - *************:6385
#    password: 123456 # 密码为空时，请将本行注释
#    timeout: 3000 # 超时时间(单位：秒)
#    lettuce: #Lettuce为Redis的Java驱动包
#      pool:
#        max-active: 8 # 连接池最大连接数
#        max-wait: -1ms  # 连接池最大阻塞等待时间（使用负值表示没有限制）
#        min-idle: 0 # 连接池中的最小空闲连接
#        max-idle: 8 # 连接池中的最大空闲连接
# SpringDoc接口文档 访问地址：http://127.0.0.1:30000/doc.html
springdoc:
  default-flat-param-object: true
  api-docs:
    enabled: true
  group-configs:
    - group: 业务模块
      packages-to-scan: com.xinghuo.modules
    - group: 对外接口
      packages-to-scan: com.xinghuo.outer
    - group: 系统管理
      packages-to-scan: com.xinghuo.system,com.xinghuo.oauth,com.xinghuo.oauth,com.xinghuo.exception,com.xinghuo.permission,com.xinghuo.scheduletask,com.xinghuo.message
    - group: 工作流
      packages-to-scan: com.xinghuo.workflow,com.xinghuo.form
    - group: 在线开发(大屏、报表、在线表单) 包路径
      packages-to-scan: com.xinghuo.visualdata,com.xinghuo.visualdev
    - group: 案例、扩展应用、APP应用 包路径
      packages-to-scan: com.xinghuo.example,com.xinghuo.extend,com.xinghuo.app

config:
  # ===================== 是否开启测试环境 =====================
  TestVersion: false
  # ===================== ApacheShardingSphere 配置开关 =====================
  sharding-sphere-enabled: false
  # ===================== 文件存储配置 =====================
  file-storage: #文件存储配置，不使用的情况下可以不写
    default-platform: local-plus-1 #默认使用的存储平台
    thumbnail-suffix: ".min.jpg" #缩略图后缀，例如【.min.jpg】【.png】
    local-plus: # 本地存储升级版
      - platform: local-plus-1 # 存储平台标识
        enable-storage: true  #启用存储
        enable-access: true #启用访问（线上请使用 Nginx 配置，效率更高）
        domain: "" # 访问域名，例如：“http://127.0.0.1:8030/”，注意后面要和 path-patterns 保持一致，“/”结尾，本地存储建议使用相对路径，方便后期更换域名
        base-path: C:/Users/<USER>/Desktop/xace2.0/xace-service/xace-resources/ # 基础路径
        path-patterns: /** # 访问路径
        storage-path:  # 存储路径
    aliyun-oss: # 阿里云 OSS ，不使用的情况下可以不写
      - platform: aliyun-oss-1 # 存储平台标识
        enable-storage: false  # 启用存储
        access-key: ??
        secret-key: ??
        end-point: ??
        bucket-name: ??
        domain: ?? # 访问域名，注意“/”结尾，例如：https://abc.oss-cn-shanghai.aliyuncs.com/
        base-path: hy/ # 基础路径
    qiniu-kodo: # 七牛云 kodo ，不使用的情况下可以不写
      - platform: qiniu-kodo-1 # 存储平台标识
        enable-storage: false  # 启用存储
        access-key: ??
        secret-key: ??
        bucket-name: ??
        domain: ?? # 访问域名，注意“/”结尾，例如：http://abc.hn-bkt.clouddn.com/
        base-path: base/ # 基础路径
    tencent-cos: # 腾讯云 COS
      - platform: tencent-cos-1 # 存储平台标识
        enable-storage: false  # 启用存储
        secret-id: ??
        secret-key: ??
        region: ?? #存仓库所在地域
        bucket-name: ??
        domain: ?? # 访问域名，注意“/”结尾，例如：https://abc.cos.ap-nanjing.myqcloud.com/
        base-path: hy/ # 基础路径
    minio: # MinIO，由于 MinIO SDK 支持 AWS S3，其它兼容 AWS S3 协议的存储平台也都可配置在这里
      - platform: minio-1 # 存储平台标识
        enable-storage: true  # 启用存储
        access-key: Q9jJs2b6Tv
        secret-key: Thj2WkpLu9DhmJyJ
        end-point: http://192.168.0.207:9000/
        bucket-name: xacesoftoss
        domain:  # 访问域名，注意“/”结尾，例如：http://minio.abc.com/abc/
        base-path:  # 基础路径

# ===================== 第三方登录配置 =====================
socials:
  # 第三方登录功能开关(false-关闭，true-开启)
  socials-enabled: false
  config:
    - # 微信
      provider: wechat_open
      client-id: your-client-id
      client-secret: your-client-secret
    - # qq
      provider: qq
      client-id: your-client-id
      client-secret: your-client-secret
    - # 企业微信
      provider: wechat_enterprise
      client-id: your-client-id
      client-secret: your-client-secret
      agentId: your-agentId
    - # 钉钉
      provider: dingtalk
      client-id: your-client-id
      client-secret: your-client-secret
      agentId: your-agentId
    - # 飞书
      provider: feishu
      client-id: your-client-id
      client-secret: your-client-secret
    - # 小程序
      provider: wechat_applets
      client-id: your-client-id
      client-secret: your-client-secret
    - # 智能桌面
      provider: sso
      client-id: frameworkservice
      client-secret: d2380065e19a63dd33507c5633363c9b
# ===================== 接口放行地址 与GatewayWhite中的默认URL合并 =====================
gateway:
  #禁止访问(主要是swagger,magicapi,actuator相关的路径)
  block-url:
  #不验证Token, 记录访问
  white-url:
  #  - /api/message/Notice
  #  - /api/permission/Users/<USER>
  #放行不记录(websocket路径..)
  exclude-url:
  #禁止访问地址的白名单IP。正式环境必须配置IP才可访问magicapi,doc.html
  white-ip:
  #  - *************
  #  - 68.64
# ===================== 任务调度配置 =====================
xxl:
  job:
    accessToken: ''
    i18n: zh_CN
    logretentiondays: 30
    triggerpool:
      fast:
        max: 200
      slow:
        max: 100
    # xxl-job服务端地址
    admin:
      addresses: http://127.0.0.1:30020/xxl-job-admin/
    executor:
      address: ''
      appname: xxl-job-executor-sample1
      ip: ''
      logpath: /data/applogs/xxl-job/jobhandler
      logretentiondays: 30
      port: 9999
  # rest调用xxl-job接口地址
  admin:
    register:
      handle-query-address: ${xxl.job.admin.addresses}api/handler/queryList
      job-info-address: ${xxl.job.admin.addresses}api/jobinfo
      log-query-address: ${xxl.job.admin.addresses}api/log
      task-list-address: ${xxl.job.admin.addresses}api/ScheduleTask/List
      task-info-address: ${xxl.job.admin.addresses}api/ScheduleTask/getInfo
      task-save-address: ${xxl.job.admin.addresses}api/ScheduleTask
      task-update-address: ${xxl.job.admin.addresses}api/ScheduleTask
      task-remove-address: ${xxl.job.admin.addresses}api/ScheduleTask/remove
      task-start-or-remove-address: ${xxl.job.admin.addresses}api/ScheduleTask/updateTask
