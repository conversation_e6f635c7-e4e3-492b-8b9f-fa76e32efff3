# 项目模板管理模块

## 概述

项目模板管理模块包含四个核心子模块，用于管理项目开发过程中的各种模板和标准配置。

## 模块结构

### 1. 阶段模板 (PhaseTemplate)
**路径**: `/src/views/project/template/phaseTemplate/`

**功能**: 管理项目阶段的标准模板
- 阶段编码和名称管理
- 标准工期设置
- 默认审批流程配置
- 默认检查单模板配置
- 状态管理（启用/禁用）

**文件结构**:
```
phaseTemplate/
├── index.vue                    # 主列表页面
├── PhaseTemplateModal.vue       # 新增/编辑弹窗
├── PhaseTemplateCopyModal.vue   # 复制弹窗
├── PhaseTemplateDetailModal.vue # 详情查看弹窗
└── phaseTemplate.data.ts        # 数据配置文件
```

### 2. 阶段计划模板 (PhasePlanTemplate)
**路径**: `/src/views/project/template/phasePlanTemplate/`

**功能**: 管理完整的阶段计划模板
- 模板名称和描述
- 知识状态管理（未发布/已发布/已归档）
- 阶段明细配置
- 统计信息展示

**文件结构**:
```
phasePlanTemplate/
├── index.vue                           # 主列表页面
├── PhasePlanTemplateModal.vue          # 新增/编辑弹窗
├── PhasePlanTemplateCopyModal.vue      # 复制弹窗
├── PhasePlanTemplateDetailModal.vue    # 详情查看弹窗
├── ApplyToProjectModal.vue             # 应用到项目弹窗
└── phasePlanTemplate.data.ts           # 数据配置文件
```

### 3. 标准交付物库 (WorkProductLibrary)
**路径**: `/src/views/project/template/workProductLibrary/`

**功能**: 管理标准交付物的定义和配置
- 交付物编码和名称
- 交付物类型和子类型
- 默认责任角色
- 属性配置（需要评审、最终交付成果、可裁剪）
- 状态管理

**文件结构**:
```
workProductLibrary/
├── index.vue                           # 主列表页面
├── WorkProductLibraryModal.vue         # 新增/编辑弹窗
├── WorkProductLibraryCopyModal.vue     # 复制弹窗
├── WorkProductLibraryDetailModal.vue   # 详情查看弹窗
└── workProductLibrary.data.ts          # 数据配置文件
```

### 4. 交付物计划模板 (WorkProductPlanTemplate)
**路径**: `/src/views/project/template/workProductPlanTemplate/`

**功能**: 管理交付物的计划模板
- 模板名称和描述
- 知识状态管理
- 交付物明细配置
- 统计信息展示

**文件结构**:
```
workProductPlanTemplate/
├── index.vue                              # 主列表页面
├── WorkProductPlanTemplateModal.vue       # 新增/编辑弹窗
├── WorkProductPlanTemplateCopyModal.vue   # 复制弹窗
├── WorkProductPlanTemplateDetailModal.vue # 详情查看弹窗
└── workProductPlanTemplate.data.ts        # 数据配置文件
```

### 5. 公共组件 (Components)
**路径**: `/src/views/project/template/components/`

**功能**: 提供模板模块间共享的组件
- UsageInfoModal.vue - 使用情况统计弹窗
- ApplyToProjectModal.vue - 应用到项目弹窗

## API 接口

### 阶段模板 API
**文件**: `/src/api/project/phaseTemplate.ts`
- 基础CRUD操作
- 批量操作
- 状态管理
- 选择列表

### 阶段计划模板 API
**文件**: `/src/api/project/phasePlanTemplate.ts`
- 基础CRUD操作
- 知识状态管理
- 发布/归档操作
- 应用到项目

### 标准交付物库 API
**文件**: `/src/api/project/workProductLibrary.ts`
- 基础CRUD操作
- 编码生成
- 名称/编码重复检查
- 状态管理

### 交付物计划模板 API
**文件**: `/src/api/project/workProductPlanTemplate.ts`
- 基础CRUD操作
- 知识状态管理
- 发布/归档操作
- 模板应用

## 后端规范化改造

### Controller层改造
所有Controller都已按照开发规范进行改造：

1. **使用VO类替代实体类**
   - 创建了对应的VO类和SelectVO类
   - getList方法返回VO对象
   - getSelectList方法返回SelectVO对象

2. **数据转换和格式化**
   - 状态名称转换
   - 布尔值文本转换
   - fullName字段构建

3. **符合开发规范**
   - option使用fullName和id
   - 保持Service层返回实体类
   - 在Controller层进行数据转换

### VO类设计
每个模块都包含两种VO类：
- **主VO类**: 用于列表和详情展示，包含所有显示字段
- **SelectVO类**: 用于选择列表，只包含id和fullName字段

## 功能特性

### 通用功能
- 列表查询（分页、排序、筛选）
- 新增/编辑/删除操作
- 批量操作
- 复制功能
- 详情查看
- 权限控制

### 特殊功能
- 状态管理（启用/禁用、发布/归档）
- 模板应用到项目
- 使用情况统计
- 编码自动生成
- 名称重复检查

## 开发规范

### 前端规范
- 使用BasicTable、BasicModal、BasicForm等标准组件
- 统一的文件命名和结构
- 完整的TypeScript类型定义
- 权限控制和表单验证

### 后端规范
- Controller层使用VO类
- Service层返回实体类
- 统一的异常处理
- 完整的Swagger文档注解

## 部署说明

1. 确保后端API接口已部署
2. 配置正确的API地址
3. 确保菜单权限已配置
4. 测试各模块功能完整性

## 后续扩展

1. 添加更多的模板类型
2. 完善模板应用逻辑
3. 增加模板版本管理
4. 添加模板导入导出功能
5. 完善使用统计和分析功能
