<template>
  <BasicModal v-bind="$attrs" @register="registerModal" title="复制交付物" @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, unref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { FormSchema } from '/@/components/Table';
  import { 
    copyWorkProductLibrary,
    checkWorkProductLibraryNameExists,
  } from '/@/api/project/workProductLibrary';

  defineOptions({ name: 'WorkProductLibraryCopyModal' });

  const emit = defineEmits(['success', 'register']);

  const rowId = ref('');
  const originalName = ref('');

  const copyFormSchema: FormSchema[] = [
    {
      field: 'newName',
      label: '新交付物名称',
      component: 'Input',
      required: true,
      componentProps: {
        placeholder: '请输入新的交付物名称',
        maxlength: 255,
      },
    },
  ];

  const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
    labelWidth: 120,
    baseColProps: { span: 24 },
    schemas: copyFormSchema,
    showActionButtonGroup: false,
    autoSubmitOnEnter: true,
  });

  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    resetFields();
    setModalProps({ confirmLoading: false });
    
    if (data?.record) {
      rowId.value = data.record.id;
      originalName.value = data.record.name;
      setFieldsValue({
        newName: `${data.record.name}_副本`,
      });
    }
  });

  async function handleSubmit() {
    try {
      const values = await validate();
      setModalProps({ confirmLoading: true });

      // 验证名称是否重复
      const nameExists = await checkWorkProductLibraryNameExists(values.newName);
      if (nameExists) {
        throw new Error('交付物名称已存在');
      }

      await copyWorkProductLibrary(rowId.value, values.newName);
      
      closeModal();
      emit('success');
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>
