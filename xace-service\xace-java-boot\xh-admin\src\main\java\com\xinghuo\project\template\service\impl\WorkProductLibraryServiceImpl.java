package com.xinghuo.project.template.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.util.core.RandomUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.project.template.dao.WorkProductLibraryMapper;
import com.xinghuo.project.template.entity.WorkProductLibraryEntity;
import com.xinghuo.project.template.enums.TemplateRelationTypeEnum;
import com.xinghuo.project.template.model.WorkProductLibraryPagination;
import com.xinghuo.project.template.model.dto.WorkProductLibraryDTO;
import com.xinghuo.project.template.service.TemplateRelationService;
import com.xinghuo.project.template.service.WorkProductLibraryService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 标准交付物库服务实现类
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Slf4j
@Service
public class WorkProductLibraryServiceImpl extends BaseServiceImpl<WorkProductLibraryMapper, WorkProductLibraryEntity> implements WorkProductLibraryService {

    @Resource
    private WorkProductLibraryMapper workProductLibraryMapper;

    @Resource
    private TemplateRelationService templateRelationService;

    @Override
    public List<WorkProductLibraryEntity> getList(WorkProductLibraryPagination pagination) {
        QueryWrapper<WorkProductLibraryEntity> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<WorkProductLibraryEntity> lambda = queryWrapper.lambda();

        // 根据交付物名称模糊查询
        if (StrXhUtil.isNotEmpty(pagination.getName())) {
            lambda.like(WorkProductLibraryEntity::getName, pagination.getName());
        }

        // 根据交付物编码精确查询
        if (StrXhUtil.isNotEmpty(pagination.getCode())) {
            lambda.eq(WorkProductLibraryEntity::getCode, pagination.getCode());
        }

        // 根据状态精确查询
        if (StrXhUtil.isNotEmpty(pagination.getStatusId())) {
            lambda.eq(WorkProductLibraryEntity::getStatusId, pagination.getStatusId());
        }

        // 根据类型精确查询
        if (StrXhUtil.isNotEmpty(pagination.getTypeId())) {
            lambda.eq(WorkProductLibraryEntity::getTypeId, pagination.getTypeId());
        }

        // 根据子类型精确查询
        if (StrXhUtil.isNotEmpty(pagination.getSubTypeId())) {
            lambda.eq(WorkProductLibraryEntity::getSubTypeId, pagination.getSubTypeId());
        }

        // 根据默认角色精确查询
        if (StrXhUtil.isNotEmpty(pagination.getDefaultRoleId())) {
            lambda.eq(WorkProductLibraryEntity::getDefaultRoleId, pagination.getDefaultRoleId());
        }

        // 根据是否需要评审查询
        if (pagination.getNeedReview() != null) {
            lambda.eq(WorkProductLibraryEntity::getNeedReview, pagination.getNeedReview());
        }

        // 根据是否是最终交付成果查询
        if (pagination.getIsDeliverable() != null) {
            lambda.eq(WorkProductLibraryEntity::getIsDeliverable, pagination.getIsDeliverable());
        }

        // 根据是否可裁剪查询
        if (pagination.getCanCut() != null) {
            lambda.eq(WorkProductLibraryEntity::getCanCut, pagination.getCanCut());
        }

        // 创建时间范围查询
        if (pagination.getCreateTimeStart() != null) {
            lambda.ge(WorkProductLibraryEntity::getCreatedAt, pagination.getCreateTimeStart());
        }
        if (pagination.getCreateTimeEnd() != null) {
            lambda.le(WorkProductLibraryEntity::getCreatedAt, pagination.getCreateTimeEnd());
        }

        // 根据关键字搜索名称、编码或描述
        String keyword = pagination.getKeyword();
        if (StrXhUtil.isNotEmpty(keyword)) {
            lambda.and(wrapper -> wrapper
                    .like(WorkProductLibraryEntity::getName, keyword)
                    .or()
                    .like(WorkProductLibraryEntity::getCode, keyword)
                    .or()
                    .like(WorkProductLibraryEntity::getDescription, keyword)
            );
        }

        // 排序：按创建时间倒序
        lambda.orderByDesc(WorkProductLibraryEntity::getCreatedAt);
        
        return processDataType(queryWrapper, pagination);
    }

    @Override
    public WorkProductLibraryDTO getDetailInfo(String id) {
        if (StrXhUtil.isEmpty(id)) {
            log.warn("查询交付物详情ID为空");
            return null;
        }

        WorkProductLibraryEntity entity = this.getById(id);
        if (entity == null) {
            return null;
        }

        WorkProductLibraryDTO dto = convertToDTO(entity);
        
        // 查询关联的项目模板ID列表
        List<String> projectTemplateIds = templateRelationService.getTargetTemplateIds(id, 
                TemplateRelationTypeEnum.WORKPRODUCT_TEMPLATE.getCode(), 
                TemplateRelationTypeEnum.PROJECT_TEMPLATE.getCode());
        dto.setProjectTemplateIds(projectTemplateIds);

        return dto;
    }

    @Override
    public WorkProductLibraryEntity getInfo(String id) {
        if (StrXhUtil.isEmpty(id)) {
            log.warn("查询交付物信息ID为空");
            return null;
        }
        return this.getById(id);
    }

    @Override
    public WorkProductLibraryEntity getByCode(String code) {
        if (StrXhUtil.isEmpty(code)) {
            log.warn("查询交付物编码为空");
            return null;
        }
        return workProductLibraryMapper.selectByCode(code);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String create(WorkProductLibraryDTO workProductDTO) {
        if (workProductDTO == null) {
            log.warn("创建交付物信息为空");
            throw new RuntimeException("交付物信息不能为空");
        }

        // 验证必填字段
        if (StrXhUtil.isEmpty(workProductDTO.getName())) {
            log.warn("交付物名称不能为空");
            throw new RuntimeException("交付物名称不能为空");
        }

        // 检查交付物名称是否重复
        boolean nameExists = isExistByName(workProductDTO.getName(), null);
        if (nameExists) {
            log.warn("交付物名称已存在: {}", workProductDTO.getName());
            throw new RuntimeException("交付物名称已存在");
        }

        // 检查交付物编码是否重复
        if (StrXhUtil.isNotEmpty(workProductDTO.getCode())) {
            boolean codeExists = isExistByCode(workProductDTO.getCode(), null);
            if (codeExists) {
                log.warn("交付物编码已存在: {}", workProductDTO.getCode());
                throw new RuntimeException("交付物编码已存在");
            }
        } else {
            // 自动生成编码
            workProductDTO.setCode(generateCode());
        }

        // 创建主表记录
        String id = RandomUtil.snowId();
        workProductDTO.setId(id);
        
        // 设置默认值
        if (workProductDTO.getNeedReview() == null) {
            workProductDTO.setNeedReview(0);
        }
        if (workProductDTO.getIsDeliverable() == null) {
            workProductDTO.setIsDeliverable(0);
        }
        if (workProductDTO.getCanCut() == null) {
            workProductDTO.setCanCut(1);
        }
        if (StrXhUtil.isEmpty(workProductDTO.getStatusId())) {
            workProductDTO.setStatusId("enabled"); // 假设enabled为启用状态
        }

        this.save(workProductDTO);

        // 保存项目模板关联关系
        if (workProductDTO.getProjectTemplateIds() != null && !workProductDTO.getProjectTemplateIds().isEmpty()) {
            templateRelationService.updateTemplateRelations(id, TemplateRelationTypeEnum.WORKPRODUCT_TEMPLATE.getCode(),
                    workProductDTO.getProjectTemplateIds(), TemplateRelationTypeEnum.PROJECT_TEMPLATE.getCode());
        }

        log.info("创建标准交付物成功, ID: {}, 名称: {}", id, workProductDTO.getName());
        return id;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(String id, WorkProductLibraryDTO workProductDTO) {
        if (StrXhUtil.isEmpty(id) || workProductDTO == null) {
            log.warn("更新交付物参数无效, ID: {}", id);
            throw new RuntimeException("更新参数无效");
        }

        // 查询原记录是否存在
        WorkProductLibraryEntity dbEntity = this.getById(id);
        if (dbEntity == null) {
            log.warn("更新的交付物不存在, ID: {}", id);
            throw new RuntimeException("交付物不存在");
        }

        // 验证必填字段
        if (StrXhUtil.isEmpty(workProductDTO.getName())) {
            log.warn("交付物名称不能为空");
            throw new RuntimeException("交付物名称不能为空");
        }

        // 检查交付物名称是否重复（排除自身）
        boolean nameExists = isExistByName(workProductDTO.getName(), id);
        if (nameExists) {
            log.warn("交付物名称已存在: {}", workProductDTO.getName());
            throw new RuntimeException("交付物名称已存在");
        }

        // 检查交付物编码是否重复（排除自身）
        if (StrXhUtil.isNotEmpty(workProductDTO.getCode())) {
            boolean codeExists = isExistByCode(workProductDTO.getCode(), id);
            if (codeExists) {
                log.warn("交付物编码已存在: {}", workProductDTO.getCode());
                throw new RuntimeException("交付物编码已存在");
            }
        }

        // 更新主表
        workProductDTO.setId(id);
        this.updateById(workProductDTO);

        // 更新项目模板关联关系
        templateRelationService.updateTemplateRelations(id, TemplateRelationTypeEnum.WORKPRODUCT_TEMPLATE.getCode(),
                workProductDTO.getProjectTemplateIds(), TemplateRelationTypeEnum.PROJECT_TEMPLATE.getCode());

        log.info("更新标准交付物成功, ID: {}, 名称: {}", id, workProductDTO.getName());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        if (StrXhUtil.isEmpty(id)) {
            log.warn("删除交付物ID为空");
            throw new RuntimeException("交付物ID不能为空");
        }

        // 查询交付物是否存在
        WorkProductLibraryEntity entity = this.getById(id);
        if (entity == null) {
            log.warn("删除的交付物不存在, ID: {}", id);
            throw new RuntimeException("交付物不存在");
        }

        // 检查交付物是否正在使用
        Map<String, Object> usageInfo = getWorkProductUsageInfo(id);
        Integer usageCount = (Integer) usageInfo.get("usageCount");
        if (usageCount != null && usageCount > 0) {
            log.warn("交付物正在使用中，不能删除, ID: {}", id);
            throw new RuntimeException("交付物正在使用中，不能删除");
        }

        // 删除关联数据
        templateRelationService.deleteBySourceTemplate(id, TemplateRelationTypeEnum.WORKPRODUCT_TEMPLATE.getCode());
        
        // 删除主表
        this.removeById(id);
        log.info("删除标准交付物成功, ID: {}, 名称: {}", id, entity.getName());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDelete(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            log.warn("批量删除交付物ID列表为空");
            throw new RuntimeException("ID列表不能为空");
        }

        for (String id : ids) {
            delete(id);
        }
        log.info("批量删除标准交付物成功, 数量: {}", ids.size());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(String id, String statusId) {
        if (StrXhUtil.isEmpty(id) || StrXhUtil.isEmpty(statusId)) {
            log.warn("更新交付物状态参数无效, ID: {}, statusId: {}", id, statusId);
            throw new RuntimeException("参数无效");
        }

        workProductLibraryMapper.updateStatus(id, statusId);
        log.info("更新交付物状态成功, ID: {}, statusId: {}", id, statusId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateStatus(List<String> ids, String statusId) {
        if (ids == null || ids.isEmpty() || StrXhUtil.isEmpty(statusId)) {
            log.warn("批量更新交付物状态参数无效");
            throw new RuntimeException("参数无效");
        }

        workProductLibraryMapper.batchUpdateStatus(ids, statusId);
        log.info("批量更新交付物状态成功, 数量: {}, statusId: {}", ids.size(), statusId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void enable(String id) {
        updateStatus(id, "enabled"); // 假设enabled为启用状态
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void disable(String id) {
        updateStatus(id, "disabled"); // 假设disabled为禁用状态
    }

    @Override
    public boolean isExistByName(String name, String excludeId) {
        if (StrXhUtil.isEmpty(name)) {
            return false;
        }

        QueryWrapper<WorkProductLibraryEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("name", name);

        // 如果有排除ID，则添加排除条件
        if (StrXhUtil.isNotEmpty(excludeId)) {
            queryWrapper.ne("id", excludeId);
        }

        long count = this.count(queryWrapper);
        return count > 0;
    }

    @Override
    public boolean isExistByCode(String code, String excludeId) {
        if (StrXhUtil.isEmpty(code)) {
            return false;
        }

        QueryWrapper<WorkProductLibraryEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("code", code);

        // 如果有排除ID，则添加排除条件
        if (StrXhUtil.isNotEmpty(excludeId)) {
            queryWrapper.ne("id", excludeId);
        }

        long count = this.count(queryWrapper);
        return count > 0;
    }

    @Override
    public List<WorkProductLibraryEntity> getSelectList(String keyword, String statusId, String typeId) {
        QueryWrapper<WorkProductLibraryEntity> queryWrapper = new QueryWrapper<>();

        // 添加关键字搜索条件
        if (StrXhUtil.isNotEmpty(keyword)) {
            queryWrapper.and(wrapper -> wrapper
                .like("name", keyword)
                .or()
                .like("code", keyword)
            );
        }

        // 添加状态筛选条件
        if (StrXhUtil.isNotEmpty(statusId)) {
            queryWrapper.eq("status_id", statusId);
        }

        // 添加类型筛选条件
        if (StrXhUtil.isNotEmpty(typeId)) {
            queryWrapper.eq("type_id", typeId);
        }

        // 按编码排序
        queryWrapper.orderByAsc("code");

        return this.list(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String copy(String id, String newName) {
        if (StrXhUtil.isEmpty(id) || StrXhUtil.isEmpty(newName)) {
            log.warn("复制交付物参数无效, ID: {}, newName: {}", id, newName);
            throw new RuntimeException("参数无效");
        }

        // 查询原交付物详情
        WorkProductLibraryDTO sourceWorkProduct = getDetailInfo(id);
        if (sourceWorkProduct == null) {
            log.warn("复制的交付物不存在, ID: {}", id);
            throw new RuntimeException("交付物不存在");
        }

        // 创建新交付物
        WorkProductLibraryDTO newWorkProduct = new WorkProductLibraryDTO();
        newWorkProduct.setName(newName);
        newWorkProduct.setDescription(sourceWorkProduct.getDescription());
        newWorkProduct.setTypeId(sourceWorkProduct.getTypeId());
        newWorkProduct.setSubTypeId(sourceWorkProduct.getSubTypeId());
        newWorkProduct.setDefaultRoleId(sourceWorkProduct.getDefaultRoleId());
        newWorkProduct.setNeedReview(sourceWorkProduct.getNeedReview());
        newWorkProduct.setIsDeliverable(sourceWorkProduct.getIsDeliverable());
        newWorkProduct.setCanCut(sourceWorkProduct.getCanCut());
        newWorkProduct.setStatusId("enabled"); // 复制的交付物默认为启用状态
        newWorkProduct.setProjectTemplateIds(sourceWorkProduct.getProjectTemplateIds());

        String newId = create(newWorkProduct);
        log.info("复制标准交付物成功, 源ID: {}, 新ID: {}, 新名称: {}", id, newId, newName);
        return newId;
    }

    @Override
    public String generateCode() {
        Integer nextSequence = workProductLibraryMapper.getNextCodeSequence();
        if (nextSequence == null) {
            nextSequence = 1;
        }
        return String.format("WP%08d", nextSequence);
    }

    @Override
    public List<Map<String, Object>> getWorkProductStatistics(Map<String, Object> params) {
        return workProductLibraryMapper.getWorkProductStatistics(params);
    }

    @Override
    public List<WorkProductLibraryEntity> getByProjectTemplateId(String projectTplId) {
        // 先通过通用关联表查询关联的交付物ID列表
        List<String> workProductIds = templateRelationService.getSourceTemplateIds(projectTplId, 
                TemplateRelationTypeEnum.PROJECT_TEMPLATE.getCode(), 
                TemplateRelationTypeEnum.WORKPRODUCT_TEMPLATE.getCode());
        
        if (workProductIds.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 根据ID列表查询交付物详情
        return this.listByIds(workProductIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProjectTemplateRelations(String workProductId, List<String> projectTemplateIds) {
        if (StrXhUtil.isEmpty(workProductId)) {
            log.warn("更新项目模板关联关系，交付物ID为空");
            throw new RuntimeException("交付物ID不能为空");
        }

        // 更新项目模板关联关系
        templateRelationService.updateTemplateRelations(workProductId, TemplateRelationTypeEnum.WORKPRODUCT_TEMPLATE.getCode(),
                projectTemplateIds, TemplateRelationTypeEnum.PROJECT_TEMPLATE.getCode());

        log.info("更新项目模板关联关系成功, 交付物ID: {}", workProductId);
    }

    @Override
    public Map<String, Object> getWorkProductUsageInfo(String id) {
        return workProductLibraryMapper.getWorkProductUsageInfo(id);
    }

    @Override
    public List<WorkProductLibraryEntity> getByType(String typeId, String subTypeId) {
        return workProductLibraryMapper.selectByType(typeId, subTypeId);
    }

    @Override
    public List<WorkProductLibraryEntity> getByDefaultRole(String defaultRoleId) {
        return workProductLibraryMapper.selectByDefaultRole(defaultRoleId);
    }

    @Override
    public List<WorkProductLibraryEntity> getByStatus(String statusId) {
        return workProductLibraryMapper.selectByStatus(statusId);
    }

    /**
     * 转换实体为DTO
     */
    private WorkProductLibraryDTO convertToDTO(WorkProductLibraryEntity entity) {
        WorkProductLibraryDTO dto = new WorkProductLibraryDTO();
        dto.setId(entity.getId());
        dto.setCode(entity.getCode());
        dto.setName(entity.getName());
        dto.setDescription(entity.getDescription());
        dto.setTypeId(entity.getTypeId());
        dto.setSubTypeId(entity.getSubTypeId());
        dto.setDefaultRoleId(entity.getDefaultRoleId());
        dto.setNeedReview(entity.getNeedReview());
        dto.setIsDeliverable(entity.getIsDeliverable());
        dto.setCanCut(entity.getCanCut());
        dto.setStatusId(entity.getStatusId());
        dto.setCreatedAt(entity.getCreatedAt());
        
        return dto;
    }
}
