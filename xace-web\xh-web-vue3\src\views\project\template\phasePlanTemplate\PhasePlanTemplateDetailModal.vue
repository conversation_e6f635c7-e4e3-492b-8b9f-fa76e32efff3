<template>
  <BasicModal 
    v-bind="$attrs" 
    @register="registerModal" 
    title="阶段计划模板详情" 
    :footer="null"
    width="1200px"
  >
    <div class="phase-plan-template-detail">
      <!-- 基本信息 -->
      <div class="detail-section">
        <h3 class="section-title">基本信息</h3>
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="模板名称">{{ templateInfo.name }}</a-descriptions-item>
          <a-descriptions-item label="知识状态">
            <a-tag :color="getStatusColor(templateInfo.knStatusId)">
              {{ getStatusText(templateInfo.knStatusId) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="模板描述" :span="2">{{ templateInfo.description || '暂无描述' }}</a-descriptions-item>
          <a-descriptions-item label="阶段总数">{{ templateInfo.phaseCount || 0 }}</a-descriptions-item>
          <a-descriptions-item label="总工期">{{ templateInfo.totalDuration || 0 }} 天</a-descriptions-item>
          <a-descriptions-item label="关联项目模板数">{{ templateInfo.projectTemplateCount || 0 }}</a-descriptions-item>
          <a-descriptions-item label="使用次数">{{ templateInfo.usageCount || 0 }}</a-descriptions-item>
          <a-descriptions-item label="创建时间">{{ templateInfo.createdAt }}</a-descriptions-item>
          <a-descriptions-item label="创建人">{{ templateInfo.creatorUserName }}</a-descriptions-item>
        </a-descriptions>
      </div>

      <!-- 阶段明细 -->
      <div class="detail-section">
        <h3 class="section-title">阶段明细</h3>
        <BasicTable @register="registerTable" />
      </div>

      <!-- 统计信息 -->
      <div class="detail-section" v-if="statisticsData">
        <h3 class="section-title">统计信息</h3>
        <a-row :gutter="16">
          <a-col :span="6">
            <a-statistic title="总阶段数" :value="statisticsData.totalPhases" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="总工期(天)" :value="statisticsData.totalDuration" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="平均工期(天)" :value="statisticsData.avgDuration" :precision="1" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="最长阶段工期(天)" :value="statisticsData.maxDuration" />
          </a-col>
        </a-row>
      </div>

      <!-- 关联信息 -->
      <div class="detail-section" v-if="relatedProjects.length > 0">
        <h3 class="section-title">关联项目模板</h3>
        <a-table 
          :columns="projectColumns" 
          :data-source="relatedProjects" 
          :pagination="false"
          size="small"
          bordered
        />
      </div>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, computed } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicTable, useTable } from '/@/components/Table';
  import { getPhasePlanTemplateDetailInfo } from '/@/api/project/phasePlanTemplate';

  defineOptions({ name: 'PhasePlanTemplateDetailModal' });

  const templateInfo = ref<any>({});
  const relatedProjects = ref<any[]>([]);

  // 阶段明细表格列
  const phaseColumns = [
    { title: '序号', dataIndex: 'seqNo', width: 80 },
    { title: '阶段编码', dataIndex: 'phaseCode', width: 120 },
    { title: '阶段名称', dataIndex: 'phaseName', width: 200 },
    { title: '标准工期(天)', dataIndex: 'stdDuration', width: 100 },
    { title: '状态', dataIndex: 'statusName', width: 80 },
    { title: '默认审批流程', dataIndex: 'defaultApprovalName', width: 150 },
    { title: '默认检查单', dataIndex: 'defaultChecklistName', width: 150 },
  ];

  // 关联项目模板表格列
  const projectColumns = [
    { title: '项目模板名称', dataIndex: 'name', width: 200 },
    { title: '项目类型', dataIndex: 'projectTypeName', width: 120 },
    { title: '状态', dataIndex: 'statusName', width: 80 },
    { title: '创建时间', dataIndex: 'createdAt', width: 180 },
    { title: '创建人', dataIndex: 'creatorUserName', width: 100 },
  ];

  const [registerTable, { setTableData }] = useTable({
    title: '阶段明细列表',
    columns: phaseColumns,
    pagination: false,
    showTableSetting: false,
    bordered: true,
    size: 'small',
  });

  const [registerModal, { setModalProps }] = useModalInner(async (data) => {
    setModalProps({ confirmLoading: true });
    
    try {
      if (data?.record) {
        const result = await getPhasePlanTemplateDetailInfo(data.record.id);
        templateInfo.value = result.data || result;
        
        // 设置阶段明细数据
        const phases = templateInfo.value.phases || [];
        setTableData(phases);
        
        // 设置关联项目数据
        relatedProjects.value = templateInfo.value.relatedProjects || [];
      }
    } catch (error) {
      console.error('获取阶段计划模板详情失败:', error);
    } finally {
      setModalProps({ confirmLoading: false });
    }
  });

  // 统计数据计算
  const statisticsData = computed(() => {
    const phases = templateInfo.value.phases || [];
    if (phases.length === 0) return null;
    
    const durations = phases.map(p => p.stdDuration || 0);
    const totalDuration = durations.reduce((sum, d) => sum + d, 0);
    const avgDuration = totalDuration / phases.length;
    const maxDuration = Math.max(...durations);
    
    return {
      totalPhases: phases.length,
      totalDuration,
      avgDuration,
      maxDuration,
    };
  });

  function getStatusColor(status: string) {
    const colorMap = {
      'draft': 'warning',
      'published': 'success',
      'archived': 'default',
    };
    return colorMap[status] || 'default';
  }

  function getStatusText(status: string) {
    const textMap = {
      'draft': '未发布',
      'published': '已发布',
      'archived': '已归档',
    };
    return textMap[status] || status;
  }
</script>

<style lang="less" scoped>
  .phase-plan-template-detail {
    .detail-section {
      margin-bottom: 24px;
      
      .section-title {
        margin-bottom: 16px;
        font-size: 16px;
        font-weight: 600;
        color: #262626;
        border-left: 4px solid #1890ff;
        padding-left: 12px;
      }
    }
  }
</style>
