package com.xinghuo.project.biz.model;

import com.xinghuo.common.base.model.Pagination;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 付款合同分页查询参数
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "付款合同分页查询参数")
public class PaymentContractPagination extends Pagination {

    /**
     * 合同编号
     */
    @Schema(description = "合同编号")
    private String cno;

    /**
     * 合同名称
     */
    @Schema(description = "合同名称")
    private String name;

    /**
     * 收款合同ID
     */
    @Schema(description = "收款合同ID")
    private String contractId;

    /**
     * 供应商ID
     */
    @Schema(description = "供应商ID")
    private String supplierId;

    /**
     * 合同状态
     */
    @Schema(description = "合同状态")
    private String status;

    /**
     * 签订日期开始
     */
    @Schema(description = "签订日期开始")
    private Date signDateStart;

    /**
     * 签订日期结束
     */
    @Schema(description = "签订日期结束")
    private Date signDateEnd;

    /**
     * 创建时间开始
     */
    @Schema(description = "创建时间开始")
    private Date createTimeStart;

    /**
     * 创建时间结束
     */
    @Schema(description = "创建时间结束")
    private Date createTimeEnd;

    /**
     * 关键字搜索（合同名称或编号）
     */
    @Schema(description = "关键字搜索")
    private String keyword;
}
