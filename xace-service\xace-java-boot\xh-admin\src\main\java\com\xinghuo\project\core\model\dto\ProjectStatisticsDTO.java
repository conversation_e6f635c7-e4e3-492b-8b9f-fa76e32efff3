package com.xinghuo.project.core.model.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 项目统计DTO
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
public class ProjectStatisticsDTO {

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 团队成员总数
     */
    private Integer totalTeamMembers;

    /**
     * 活跃团队成员数
     */
    private Integer activeTeamMembers;

    /**
     * 任务总数
     */
    private Integer totalTasks;

    /**
     * 已完成任务数
     */
    private Integer completedTasks;

    /**
     * 进行中任务数
     */
    private Integer inProgressTasks;

    /**
     * 待开始任务数
     */
    private Integer pendingTasks;

    /**
     * 逾期任务数
     */
    private Integer overdueTasks;

    /**
     * 里程碑总数
     */
    private Integer totalMilestones;

    /**
     * 已完成里程碑数
     */
    private Integer completedMilestones;

    /**
     * 风险总数
     */
    private Integer totalRisks;

    /**
     * 高风险数量
     */
    private Integer highRisks;

    /**
     * 中风险数量
     */
    private Integer mediumRisks;

    /**
     * 低风险数量
     */
    private Integer lowRisks;

    /**
     * 问题总数
     */
    private Integer totalIssues;

    /**
     * 未解决问题数
     */
    private Integer openIssues;

    /**
     * 已解决问题数
     */
    private Integer resolvedIssues;

    /**
     * 变更请求总数
     */
    private Integer totalChangeRequests;

    /**
     * 待审批变更数
     */
    private Integer pendingChanges;

    /**
     * 已批准变更数
     */
    private Integer approvedChanges;

    /**
     * 已拒绝变更数
     */
    private Integer rejectedChanges;

    /**
     * 交付物总数
     */
    private Integer totalDeliverables;

    /**
     * 已交付数量
     */
    private Integer deliveredCount;

    /**
     * 待交付数量
     */
    private Integer pendingDeliverables;

    /**
     * 质量检查总数
     */
    private Integer totalQualityChecks;

    /**
     * 通过质量检查数
     */
    private Integer passedQualityChecks;

    /**
     * 预算总额
     */
    private BigDecimal totalBudget;

    /**
     * 已使用预算
     */
    private BigDecimal usedBudget;

    /**
     * 剩余预算
     */
    private BigDecimal remainingBudget;

    /**
     * 预算使用率
     */
    private Double budgetUtilizationRate;

    /**
     * 实际成本
     */
    private BigDecimal actualCost;

    /**
     * 成本偏差
     */
    private BigDecimal costVariance;

    /**
     * 成本绩效指数
     */
    private Double costPerformanceIndex;

    /**
     * 进度绩效指数
     */
    private Double schedulePerformanceIndex;

    /**
     * 工作量总计（人天）
     */
    private Double totalWorkload;

    /**
     * 已完成工作量
     */
    private Double completedWorkload;

    /**
     * 剩余工作量
     */
    private Double remainingWorkload;

    /**
     * 工作效率
     */
    private Double workEfficiency;

    /**
     * 项目健康度评分
     */
    private Double healthScore;

    /**
     * 客户满意度评分
     */
    private Double customerSatisfactionScore;

    /**
     * 团队满意度评分
     */
    private Double teamSatisfactionScore;

    /**
     * 质量评分
     */
    private Double qualityScore;

    /**
     * 按时交付率
     */
    private Double onTimeDeliveryRate;

    /**
     * 缺陷密度
     */
    private Double defectDensity;

    /**
     * 返工率
     */
    private Double reworkRate;

    /**
     * 生产力指标
     */
    private Double productivityIndex;

    /**
     * 最后更新时间
     */
    private java.util.Date lastUpdateTime;

    /**
     * 统计周期开始时间
     */
    private java.util.Date statisticsPeriodStart;

    /**
     * 统计周期结束时间
     */
    private java.util.Date statisticsPeriodEnd;
}
