<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinghuo.checkscore.dao.CheckHisScoreMapper">

    <!-- 结果映射，包含分组信息 -->
    <resultMap id="CheckHisScoreWithGroupResultMap" type="com.xinghuo.checkscore.entity.CheckHisScoreEntity">
        <id property="id" column="id" />
        <result property="month" column="month" />
        <result property="userId" column="user_id" />
        <result property="userName" column="user_name" />
        <result property="parentUserName" column="parent_user_name" />
        <result property="fbName" column="fb_name" />
        <result property="fbId" column="fb_id" />
        <result property="actScore" column="act_score" />
        <result property="scoreNote" column="score_note" />
        <result property="reviewActDays" column="review_act_days" />
        <result property="reviewHolidays" column="review_holidays" />
        <result property="groupId" column="group_id" />
        <result property="groupName" column="group_name" />
        <result property="creatorTime" column="f_creatortime" />
        <result property="creatorUserId" column="f_creatoruserid" />
    </resultMap>

    <!-- 基础查询条件 -->
    <sql id="baseWhereCondition">
        <where>
            <if test="pagination.startMonth != null and pagination.startMonth != '' and pagination.endMonth != null and pagination.endMonth != '' and pagination.startMonth == pagination.endMonth">
                AND h.month = #{pagination.startMonth}
            </if>
            <if test="pagination.month != null and pagination.month != ''">
                AND h.month = #{pagination.month}
            </if>
            <if test="pagination.startMonth != null and pagination.startMonth != '' and pagination.endMonth != null and pagination.endMonth != '' and pagination.startMonth != pagination.endMonth">
                AND h.month BETWEEN #{pagination.startMonth} AND #{pagination.endMonth}
            </if>
            <if test="pagination.userName != null and pagination.userName != ''">
                AND h.user_name LIKE CONCAT('%', #{pagination.userName}, '%')
            </if>
            <if test="pagination.userId != null and pagination.userId != ''">
                AND h.user_id = #{pagination.userId}
            </if>
            <if test="pagination.fbId != null and pagination.fbId != ''">
                AND h.fb_id = #{pagination.fbId}
            </if>
        </where>
    </sql>

    <!-- 获取绩效列表（包含分组信息） -->
    <select id="getListWithGroupInfo" resultMap="CheckHisScoreWithGroupResultMap">
        SELECT
            h.f_id as id,
            h.month,
            h.user_id,
            h.user_name,
            h.parent_user_name,
            h.fb_name,
            h.fb_id,
            h.act_score,
            h.score_note,
            h.review_act_days,
            h.review_holidays,
            h.f_creatortime,
            h.f_creatoruserid,
            u.f_groupid as group_id,
            g.f_fullname as group_name
        FROM zz_jx_his_score h
        LEFT JOIN base_user u ON h.user_id = u.f_id
        LEFT JOIN base_group g ON u.f_groupid = g.f_id
        <include refid="baseWhereCondition" />
        ORDER BY h.f_creatortime DESC
        <if test="pagination.currentPage != null and pagination.pageSize != null">
            LIMIT #{pagination.offset}, #{pagination.pageSize}
        </if>
    </select>

    <!-- 获取绩效列表总数（包含分组信息） -->
    <select id="getCountWithGroupInfo" resultType="long">
        SELECT COUNT(1)
        FROM zz_jx_his_score h
        LEFT JOIN base_user u ON h.user_id = u.f_id
        LEFT JOIN base_group g ON u.f_groupid = g.f_id
        <include refid="baseWhereCondition" />
    </select>

</mapper>