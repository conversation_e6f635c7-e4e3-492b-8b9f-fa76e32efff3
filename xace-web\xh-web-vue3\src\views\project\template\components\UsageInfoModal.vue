<template>
  <BasicModal 
    v-bind="$attrs" 
    @register="registerModal" 
    title="使用情况统计" 
    :footer="null"
    width="800px"
  >
    <div class="usage-info">
      <!-- 基本统计 -->
      <div class="usage-section">
        <h3 class="section-title">基本统计</h3>
        <a-row :gutter="16">
          <a-col :span="8">
            <a-statistic title="总使用次数" :value="usageInfo.totalUsage || 0" />
          </a-col>
          <a-col :span="8">
            <a-statistic title="关联项目数" :value="usageInfo.projectCount || 0" />
          </a-col>
          <a-col :span="8">
            <a-statistic title="活跃项目数" :value="usageInfo.activeProjectCount || 0" />
          </a-col>
        </a-row>
      </div>

      <!-- 使用趋势 -->
      <div class="usage-section" v-if="usageInfo.usageTrend && usageInfo.usageTrend.length > 0">
        <h3 class="section-title">使用趋势</h3>
        <a-table 
          :columns="trendColumns" 
          :data-source="usageInfo.usageTrend" 
          :pagination="false"
          size="small"
          bordered
        />
      </div>

      <!-- 关联项目列表 -->
      <div class="usage-section" v-if="usageInfo.relatedProjects && usageInfo.relatedProjects.length > 0">
        <h3 class="section-title">关联项目列表</h3>
        <a-table 
          :columns="projectColumns" 
          :data-source="usageInfo.relatedProjects" 
          :pagination="{ pageSize: 10 }"
          size="small"
          bordered
        />
      </div>

      <!-- 最近使用记录 -->
      <div class="usage-section" v-if="usageInfo.recentUsage && usageInfo.recentUsage.length > 0">
        <h3 class="section-title">最近使用记录</h3>
        <a-table 
          :columns="recentColumns" 
          :data-source="usageInfo.recentUsage" 
          :pagination="{ pageSize: 10 }"
          size="small"
          bordered
        />
      </div>

      <!-- 空状态 -->
      <div v-if="!hasUsageData" class="empty-state">
        <a-empty description="暂无使用记录" />
      </div>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, computed } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';

  defineOptions({ name: 'UsageInfoModal' });

  const usageInfo = ref<any>({});

  // 使用趋势表格列
  const trendColumns = [
    { title: '时间', dataIndex: 'period', width: 120 },
    { title: '使用次数', dataIndex: 'count', width: 100 },
    { title: '新增项目数', dataIndex: 'newProjects', width: 120 },
    { title: '活跃项目数', dataIndex: 'activeProjects', width: 120 },
  ];

  // 关联项目表格列
  const projectColumns = [
    { title: '项目名称', dataIndex: 'projectName', width: 200 },
    { title: '项目编码', dataIndex: 'projectCode', width: 120 },
    { title: '项目状态', dataIndex: 'projectStatus', width: 100 },
    { title: '使用时间', dataIndex: 'usedAt', width: 180 },
    { title: '使用人', dataIndex: 'usedBy', width: 100 },
  ];

  // 最近使用记录表格列
  const recentColumns = [
    { title: '操作类型', dataIndex: 'actionType', width: 100 },
    { title: '项目名称', dataIndex: 'projectName', width: 200 },
    { title: '操作时间', dataIndex: 'actionTime', width: 180 },
    { title: '操作人', dataIndex: 'actionBy', width: 100 },
    { title: '备注', dataIndex: 'remark', width: 150 },
  ];

  const [registerModal, { setModalProps }] = useModalInner(async (data) => {
    setModalProps({ confirmLoading: true });
    
    try {
      if (data?.usageInfo) {
        usageInfo.value = data.usageInfo;
      } else if (data?.record && data?.getUsageInfo) {
        // 如果传入了获取使用信息的函数，则调用它
        const result = await data.getUsageInfo(data.record.id);
        usageInfo.value = result.data || result;
      }
    } catch (error) {
      console.error('获取使用情况失败:', error);
      usageInfo.value = {};
    } finally {
      setModalProps({ confirmLoading: false });
    }
  });

  // 是否有使用数据
  const hasUsageData = computed(() => {
    const info = usageInfo.value;
    return (
      (info.totalUsage && info.totalUsage > 0) ||
      (info.usageTrend && info.usageTrend.length > 0) ||
      (info.relatedProjects && info.relatedProjects.length > 0) ||
      (info.recentUsage && info.recentUsage.length > 0)
    );
  });
</script>

<style lang="less" scoped>
  .usage-info {
    .usage-section {
      margin-bottom: 24px;
      
      .section-title {
        margin-bottom: 16px;
        font-size: 16px;
        font-weight: 600;
        color: #262626;
        border-left: 4px solid #1890ff;
        padding-left: 12px;
      }
    }

    .empty-state {
      text-align: center;
      padding: 40px 0;
    }
  }
</style>
