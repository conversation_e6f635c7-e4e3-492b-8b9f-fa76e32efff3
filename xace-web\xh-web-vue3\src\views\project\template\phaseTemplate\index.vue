<template>
  <div>
    <BasicTable @register="registerTable" :searchInfo="searchInfo">
      <template #toolbar>
        <a-button type="primary" @click="handleCreate" v-auth="'project:template:phaseTemplate:create'">
          <Icon icon="ant-design:plus-outlined" />
          新增阶段模板
        </a-button>
        <a-button 
          type="primary" 
          ghost 
          @click="handleBatchEnable" 
          :disabled="!hasSelected"
          v-auth="'project:template:phaseTemplate:updateStatus'"
        >
          <Icon icon="ant-design:check-outlined" />
          批量启用
        </a-button>
        <a-button 
          type="primary" 
          ghost 
          @click="handleBatchDisable" 
          :disabled="!hasSelected"
          v-auth="'project:template:phaseTemplate:updateStatus'"
        >
          <Icon icon="ant-design:stop-outlined" />
          批量禁用
        </a-button>
        <a-button 
          color="error" 
          @click="handleBatchDelete" 
          :disabled="!hasSelected"
          v-auth="'project:template:phaseTemplate:delete'"
        >
          <Icon icon="ant-design:delete-outlined" />
          批量删除
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'clarity:info-standard-line',
                tooltip: '查看详情',
                onClick: handleView.bind(null, record),
                auth: 'project:template:phaseTemplate:view',
              },
              {
                icon: 'clarity:note-edit-line',
                tooltip: '编辑',
                onClick: handleEdit.bind(null, record),
                auth: 'project:template:phaseTemplate:edit',
              },
              {
                icon: 'ant-design:copy-outlined',
                tooltip: '复制',
                onClick: handleCopy.bind(null, record),
                auth: 'project:template:phaseTemplate:copy',
              },
              {
                icon: 'ant-design:delete-outlined',
                color: 'error',
                tooltip: '删除',
                popConfirm: {
                  title: '是否确认删除',
                  placement: 'left',
                  confirm: handleDelete.bind(null, record),
                },
                auth: 'project:template:phaseTemplate:delete',
              },
            ]"
            :dropDownActions="[
              {
                label: record.status === 1 ? '禁用' : '启用',
                onClick: record.status === 1 ? handleDisable.bind(null, record) : handleEnable.bind(null, record),
                auth: 'project:template:phaseTemplate:updateStatus',
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <PhaseTemplateModal @register="registerModal" @success="handleSuccess" />
    <PhaseTemplateCopyModal @register="registerCopyModal" @success="handleSuccess" />
    <PhaseTemplateDetailModal @register="registerDetailModal" />
  </div>
</template>

<script lang="ts" setup>
  import { reactive, computed } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { useModal } from '/@/components/Modal';
  import { Icon } from '/@/components/Icon';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { usePermission } from '/@/hooks/web/usePermission';
  
  import PhaseTemplateModal from './PhaseTemplateModal.vue';
  import PhaseTemplateCopyModal from './PhaseTemplateCopyModal.vue';
  import PhaseTemplateDetailModal from './PhaseTemplateDetailModal.vue';
  import { columns, searchFormSchema } from './phaseTemplate.data';
  import {
    getPhaseTemplateList,
    deletePhaseTemplate,
    batchDeletePhaseTemplate,
    enablePhaseTemplate,
    disablePhaseTemplate,
    batchUpdatePhaseTemplateStatus,
  } from '/@/api/project/phaseTemplate';

  defineOptions({ name: 'project.template.PhaseTemplate' });

  const { createMessage, createConfirm } = useMessage();
  const { hasPermission } = usePermission();
  const [registerModal, { openModal }] = useModal();
  const [registerCopyModal, { openModal: openCopyModal }] = useModal();
  const [registerDetailModal, { openModal: openDetailModal }] = useModal();
  const searchInfo = reactive<Recordable>({});

  const [registerTable, { reload, getSelectRows }] = useTable({
    title: '阶段模板列表',
    api: getPhaseTemplateList,
    rowKey: 'id',
    columns,
    formConfig: {
      labelWidth: 120,
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
    },
    useSearchForm: true,
    showTableSetting: true,
    bordered: true,
    rowSelection: {
      type: 'checkbox',
    },
    actionColumn: {
      width: 120,
      title: '操作',
      dataIndex: 'action',
      fixed: undefined,
    },
  });

  const hasSelected = computed(() => getSelectRows().length > 0);

  function handleCreate() {
    openModal(true, {
      isUpdate: false,
    });
  }

  function handleEdit(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
    });
  }

  function handleView(record: Recordable) {
    openDetailModal(true, {
      record,
    });
  }

  function handleCopy(record: Recordable) {
    openCopyModal(true, {
      record,
    });
  }

  async function handleDelete(record: Recordable) {
    try {
      await deletePhaseTemplate(record.id);
      createMessage.success('删除成功');
      reload();
    } catch (error) {
      createMessage.error('删除失败');
    }
  }

  async function handleBatchDelete() {
    const selectedRows = getSelectRows();
    if (selectedRows.length === 0) {
      createMessage.warning('请选择要删除的记录');
      return;
    }

    createConfirm({
      iconType: 'warning',
      title: '确认删除',
      content: `确定要删除选中的 ${selectedRows.length} 条记录吗？`,
      onOk: async () => {
        try {
          const ids = selectedRows.map(row => row.id);
          await batchDeletePhaseTemplate(ids);
          createMessage.success('批量删除成功');
          reload();
        } catch (error) {
          createMessage.error('批量删除失败');
        }
      },
    });
  }

  async function handleEnable(record: Recordable) {
    try {
      await enablePhaseTemplate(record.id);
      createMessage.success('启用成功');
      reload();
    } catch (error) {
      createMessage.error('启用失败');
    }
  }

  async function handleDisable(record: Recordable) {
    try {
      await disablePhaseTemplate(record.id);
      createMessage.success('禁用成功');
      reload();
    } catch (error) {
      createMessage.error('禁用失败');
    }
  }

  async function handleBatchEnable() {
    const selectedRows = getSelectRows();
    if (selectedRows.length === 0) {
      createMessage.warning('请选择要启用的记录');
      return;
    }

    try {
      const ids = selectedRows.map(row => row.id);
      await batchUpdatePhaseTemplateStatus(ids, 1);
      createMessage.success('批量启用成功');
      reload();
    } catch (error) {
      createMessage.error('批量启用失败');
    }
  }

  async function handleBatchDisable() {
    const selectedRows = getSelectRows();
    if (selectedRows.length === 0) {
      createMessage.warning('请选择要禁用的记录');
      return;
    }

    try {
      const ids = selectedRows.map(row => row.id);
      await batchUpdatePhaseTemplateStatus(ids, 0);
      createMessage.success('批量禁用成功');
      reload();
    } catch (error) {
      createMessage.error('批量禁用失败');
    }
  }

  function handleSuccess() {
    reload();
  }
</script>
