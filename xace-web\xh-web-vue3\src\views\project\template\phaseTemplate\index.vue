<template>
  <div>
    <BasicTable @register="registerTable" :searchInfo="searchInfo">
      <template #toolbar>
        <a-button type="primary" @click="handleCreate">
          <Icon icon="ant-design:plus-outlined" />
          新增阶段模板
        </a-button>
        <a-button
          type="primary"
          ghost
          @click="handleBatchEnable"
          :disabled="!hasSelected"
        >
          <Icon icon="ant-design:check-outlined" />
          批量启用
        </a-button>
        <a-button 
          type="primary" 
          ghost 
          @click="handleBatchDisable" 
          :disabled="!hasSelected"

        >
          <Icon icon="ant-design:stop-outlined" />
          批量禁用
        </a-button>
        <a-button 
          color="error" 
          @click="handleBatchDelete" 
          :disabled="!hasSelected"

        >
          <Icon icon="ant-design:delete-outlined" />
          批量删除
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'clarity:info-standard-line',
                label: '查看详情',
                onClick: handleView.bind(null, record),
              },
              {
                icon: 'clarity:note-edit-line',
                label: '编辑',
                onClick: handleEdit.bind(null, record),
              },
              {
                icon: 'ant-design:copy-outlined',
                label: '复制',
                onClick: handleCopy.bind(null, record),
              },
              {
                icon: 'ant-design:delete-outlined',
                color: 'error',
                label: '删除',
                popConfirm: {
                  title: '是否确认删除',
                  placement: 'left',
                  confirm: handleDelete.bind(null, record),
                },
              },
            ]"
            :dropDownActions="[
              {
                label: record.status === 1 ? '禁用' : '启用',
                onClick: record.status === 1 ? handleDisable.bind(null, record) : handleEnable.bind(null, record),

              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <FormDrawer @register="registerFormDrawer" @reload="handleSuccess" />
    <DetailDrawer @register="registerDetailDrawer" />
    <PhaseTemplateCopyModal @register="registerCopyModal" @success="handleSuccess" />
  </div>
</template>

<script lang="ts" setup>
  import { reactive, computed } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { useModal } from '/@/components/Modal';
  import { useDrawer } from '/@/components/Drawer';
  import { Icon } from '/@/components/Icon';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { usePermission } from '/@/hooks/web/usePermission';
  
  import FormDrawer from './FormDrawer.vue';
  import DetailDrawer from './DetailDrawer.vue';
  import PhaseTemplateCopyModal from './PhaseTemplateCopyModal.vue';
  import { columns, searchFormSchema } from './phaseTemplate.data';
  import {
    getPhaseTemplateList,
    deletePhaseTemplate,
    batchDeletePhaseTemplate,
    enablePhaseTemplate,
    disablePhaseTemplate,
    batchUpdatePhaseTemplateStatus,
  } from '/@/api/project/phaseTemplate';

  defineOptions({ name: 'project.template.PhaseTemplate' });

  const { createMessage, createConfirm } = useMessage();
  const { hasPermission } = usePermission();
  const [registerFormDrawer, { openDrawer: openFormDrawer }] = useDrawer();
  const [registerDetailDrawer, { openDrawer: openDetailDrawer }] = useDrawer();
  const [registerCopyModal, { openModal: openCopyModal }] = useModal();
  const searchInfo = reactive<Recordable>({});

  const [registerTable, { reload, getSelectRows }] = useTable({
    title: '阶段模板列表',
    api: getPhaseTemplateList,
    rowKey: 'id',
    columns,
    formConfig: {
      labelWidth: 120,
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
    },
    useSearchForm: true,
    showTableSetting: true,
    bordered: true,
    rowSelection: {
      type: 'checkbox',
    },
    actionColumn: {
      width: 120,
      title: '操作',
      dataIndex: 'action',
      fixed: undefined,
    },
  });

  const hasSelected = computed(() => getSelectRows().length > 0);

  function handleCreate() {
    openFormDrawer(true, {
      isUpdate: false,
    });
  }

  function handleEdit(record: Recordable) {
    openFormDrawer(true, {
      record,
      isUpdate: true,
    });
  }

  function handleView(record: Recordable) {
    openDetailDrawer(true, {
      id: record.id,
    });
  }

  function handleCopy(record: Recordable) {
    openCopyModal(true, {
      record,
    });
  }

  async function handleDelete(record: Recordable) {
    try {
      await deletePhaseTemplate(record.id);
      createMessage.success('删除成功');
      reload();
    } catch (error) {
      createMessage.error('删除失败');
    }
  }

  async function handleBatchDelete() {
    const selectedRows = getSelectRows();
    if (selectedRows.length === 0) {
      createMessage.warning('请选择要删除的记录');
      return;
    }

    createConfirm({
      iconType: 'warning',
      title: '确认删除',
      content: `确定要删除选中的 ${selectedRows.length} 条记录吗？`,
      onOk: async () => {
        try {
          const ids = selectedRows.map(row => row.id);
          await batchDeletePhaseTemplate(ids);
          createMessage.success('批量删除成功');
          reload();
        } catch (error) {
          createMessage.error('批量删除失败');
        }
      },
    });
  }

  async function handleEnable(record: Recordable) {
    try {
      await enablePhaseTemplate(record.id);
      createMessage.success('启用成功');
      reload();
    } catch (error) {
      createMessage.error('启用失败');
    }
  }

  async function handleDisable(record: Recordable) {
    try {
      await disablePhaseTemplate(record.id);
      createMessage.success('禁用成功');
      reload();
    } catch (error) {
      createMessage.error('禁用失败');
    }
  }

  async function handleBatchEnable() {
    const selectedRows = getSelectRows();
    if (selectedRows.length === 0) {
      createMessage.warning('请选择要启用的记录');
      return;
    }

    try {
      const ids = selectedRows.map(row => row.id);
      await batchUpdatePhaseTemplateStatus(ids, 1);
      createMessage.success('批量启用成功');
      reload();
    } catch (error) {
      createMessage.error('批量启用失败');
    }
  }

  async function handleBatchDisable() {
    const selectedRows = getSelectRows();
    if (selectedRows.length === 0) {
      createMessage.warning('请选择要禁用的记录');
      return;
    }

    try {
      const ids = selectedRows.map(row => row.id);
      await batchUpdatePhaseTemplateStatus(ids, 0);
      createMessage.success('批量禁用成功');
      reload();
    } catch (error) {
      createMessage.error('批量禁用失败');
    }
  }

  function handleSuccess() {
    reload();
  }
</script>
