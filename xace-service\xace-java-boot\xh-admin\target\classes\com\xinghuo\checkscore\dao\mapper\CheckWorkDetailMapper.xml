<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinghuo.checkscore.dao.CheckWorkDetailMapper">
    <select id="updateCheckWorkDetail">
        {call updateCheckWorkDetail(#{all}, #{userId})}
    </select>


    <select id="listRelationUserIdList" resultType="java.lang.String">
        select distinct parent_user_id
        from zz_jx_check_user_relation_auto
        where user_id = #{userId}
          and parent_user_id != ''
    </select>
</mapper>