<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="getTitle" @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, computed, unref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { formSchema } from './workProductPlanTemplate.data';
  import { 
    createWorkProductPlanTemplate, 
    updateWorkProductPlanTemplate,
    checkWorkProductPlanTemplateNameExists,
  } from '/@/api/project/workProductPlanTemplate';

  defineOptions({ name: 'WorkProductPlanTemplateModal' });

  const emit = defineEmits(['success', 'register']);

  const isUpdate = ref(true);
  const rowId = ref('');

  const [registerForm, { setFieldsValue, updateSchema, resetFields, validate }] = useForm({
    labelWidth: 120,
    baseColProps: { span: 24 },
    schemas: formSchema,
    showActionButtonGroup: false,
    autoSubmitOnEnter: true,
  });

  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    resetFields();
    setModalProps({ confirmLoading: false });
    isUpdate.value = !!data?.isUpdate;

    if (unref(isUpdate)) {
      rowId.value = data.record.id;
      setFieldsValue({
        ...data.record,
      });
    }

    updateSchema([
      {
        field: 'knStatusId',
        componentProps: {
          disabled: unref(isUpdate) && data.record?.knStatusId === 'published',
        },
      },
    ]);
  });

  const getTitle = computed(() => (!unref(isUpdate) ? '新增交付物计划模板' : '编辑交付物计划模板'));

  async function handleSubmit() {
    try {
      const values = await validate();
      setModalProps({ confirmLoading: true });

      // 验证名称是否重复
      const nameExists = await checkWorkProductPlanTemplateNameExists(
        values.name, 
        unref(isUpdate) ? rowId.value : undefined
      );
      if (nameExists) {
        throw new Error('模板名称已存在');
      }

      if (unref(isUpdate)) {
        await updateWorkProductPlanTemplate(rowId.value, values);
      } else {
        await createWorkProductPlanTemplate(values);
      }

      closeModal();
      emit('success', { isUpdate: unref(isUpdate), values: { ...values, id: rowId.value } });
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>
