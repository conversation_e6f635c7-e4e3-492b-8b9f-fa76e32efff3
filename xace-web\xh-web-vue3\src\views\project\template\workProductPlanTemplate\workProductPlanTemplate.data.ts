import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { h } from 'vue';
import { Tag } from 'ant-design-vue';

export const columns: BasicColumn[] = [
  {
    title: '模板名称',
    dataIndex: 'name',
    width: 200,
    sorter: true,
  },
  {
    title: '模板描述',
    dataIndex: 'description',
    width: 250,
    ellipsis: true,
  },
  {
    title: '知识状态',
    dataIndex: 'knStatusName',
    width: 100,
    customRender: ({ record }) => {
      const status = record.knStatusId;
      let color = 'default';
      let text = record.knStatusName || status;
      
      switch (status) {
        case 'draft':
          color = 'warning';
          text = '未发布';
          break;
        case 'published':
          color = 'success';
          text = '已发布';
          break;
        case 'archived':
          color = 'default';
          text = '已归档';
          break;
      }
      
      return h(Tag, { color }, () => text);
    },
  },
  {
    title: '交付物总数',
    dataIndex: 'workProductCount',
    width: 100,
    customRender: ({ record }) => {
      return record.workProductCount || 0;
    },
  },
  {
    title: '需评审数量',
    dataIndex: 'reviewRequiredCount',
    width: 100,
    customRender: ({ record }) => {
      return record.reviewRequiredCount || 0;
    },
  },
  {
    title: '最终交付成果数量',
    dataIndex: 'deliverableCount',
    width: 120,
    customRender: ({ record }) => {
      return record.deliverableCount || 0;
    },
  },
  {
    title: '关联项目模板数',
    dataIndex: 'projectTemplateCount',
    width: 120,
    customRender: ({ record }) => {
      return record.projectTemplateCount || 0;
    },
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    width: 180,
    sorter: true,
  },
  {
    title: '创建人',
    dataIndex: 'creatorUserName',
    width: 100,
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    field: 'keyword',
    label: '关键字',
    component: 'Input',
    componentProps: {
      placeholder: '请输入模板名称',
    },
    colProps: { span: 8 },
  },
  {
    field: 'knStatusId',
    label: '知识状态',
    component: 'Select',
    componentProps: {
      placeholder: '请选择知识状态',
      options: [
        { label: '未发布', value: 'draft' },
        { label: '已发布', value: 'published' },
        { label: '已归档', value: 'archived' },
      ],
    },
    colProps: { span: 8 },
  },
];

export const formSchema: FormSchema[] = [
  {
    field: 'id',
    label: 'ID',
    component: 'Input',
    show: false,
  },
  {
    field: 'name',
    label: '模板名称',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入模板名称',
      maxlength: 255,
    },
    colProps: { span: 24 },
  },
  {
    field: 'description',
    label: '模板描述',
    component: 'InputTextArea',
    componentProps: {
      placeholder: '请输入模板描述',
      rows: 4,
      maxlength: 1000,
      showCount: true,
    },
    colProps: { span: 24 },
  },
  {
    field: 'knStatusId',
    label: '知识状态',
    component: 'RadioButtonGroup',
    defaultValue: 'draft',
    componentProps: {
      options: [
        { label: '未发布', value: 'draft' },
        { label: '已发布', value: 'published' },
        { label: '已归档', value: 'archived' },
      ],
    },
    colProps: { span: 24 },
  },
];

// 复制表单配置
export const copyFormSchema: FormSchema[] = [
  {
    field: 'newName',
    label: '新模板名称',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入新的模板名称',
      maxlength: 255,
    },
  },
];

// 交付物明细表格列配置
export const detailColumns: BasicColumn[] = [
  {
    title: '序号',
    dataIndex: 'seqNo',
    width: 80,
    sorter: true,
  },
  {
    title: '交付物名称',
    dataIndex: 'name',
    width: 200,
  },
  {
    title: '交付物编码',
    dataIndex: 'code',
    width: 120,
  },
  {
    title: '阶段模板',
    dataIndex: 'stageTemplateName',
    width: 150,
  },
  {
    title: '活动模板',
    dataIndex: 'activityTemplateName',
    width: 150,
  },
  {
    title: '交付物类型',
    dataIndex: 'typeName',
    width: 120,
  },
  {
    title: '责任角色',
    dataIndex: 'responseRoleName',
    width: 120,
  },
  {
    title: '需要评审',
    dataIndex: 'needReview',
    width: 80,
    customRender: ({ record }) => {
      const status = record.needReview;
      const color = status === 1 ? 'success' : 'default';
      const text = status === 1 ? '是' : '否';
      return h(Tag, { color }, () => text);
    },
  },
  {
    title: '最终交付成果',
    dataIndex: 'isDeliverable',
    width: 100,
    customRender: ({ record }) => {
      const status = record.isDeliverable;
      const color = status === 1 ? 'processing' : 'default';
      const text = status === 1 ? '是' : '否';
      return h(Tag, { color }, () => text);
    },
  },
  {
    title: '可裁剪',
    dataIndex: 'canCut',
    width: 80,
    customRender: ({ record }) => {
      const status = record.canCut;
      const color = status === 1 ? 'warning' : 'default';
      const text = status === 1 ? '可' : '不可';
      return h(Tag, { color }, () => text);
    },
  },
];
