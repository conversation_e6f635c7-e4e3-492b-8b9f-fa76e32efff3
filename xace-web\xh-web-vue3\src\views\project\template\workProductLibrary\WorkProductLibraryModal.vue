<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="getTitle" @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, computed, unref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { formSchema } from './workProductLibrary.data';
  import { 
    createWorkProductLibrary, 
    updateWorkProductLibrary,
    generateWorkProductLibraryCode,
    checkWorkProductLibraryNameExists,
    checkWorkProductLibraryCodeExists,
  } from '/@/api/project/workProductLibrary';

  defineOptions({ name: 'WorkProductLibraryModal' });

  const emit = defineEmits(['success', 'register']);

  const isUpdate = ref(true);
  const rowId = ref('');

  const [registerForm, { setFieldsValue, updateSchema, resetFields, validate }] = useForm({
    labelWidth: 100,
    baseColProps: { span: 24 },
    schemas: formSchema,
    showActionButtonGroup: false,
    autoSubmitOnEnter: true,
  });

  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    resetFields();
    setModalProps({ confirmLoading: false });
    isUpdate.value = !!data?.isUpdate;

    if (unref(isUpdate)) {
      rowId.value = data.record.id;
      setFieldsValue({
        ...data.record,
      });
    } else {
      // 新建时自动生成编码
      try {
        const code = await generateWorkProductLibraryCode();
        setFieldsValue({
          code: code,
        });
      } catch (error) {
        console.error('生成编码失败:', error);
      }
    }

    updateSchema([
      {
        field: 'code',
        componentProps: {
          disabled: unref(isUpdate),
        },
      },
    ]);
  });

  const getTitle = computed(() => (!unref(isUpdate) ? '新增交付物' : '编辑交付物'));

  async function handleSubmit() {
    try {
      const values = await validate();
      setModalProps({ confirmLoading: true });

      // 验证名称是否重复
      const nameExists = await checkWorkProductLibraryNameExists(
        values.name, 
        unref(isUpdate) ? rowId.value : undefined
      );
      if (nameExists) {
        throw new Error('交付物名称已存在');
      }

      // 验证编码是否重复（仅新建时）
      if (!unref(isUpdate) && values.code) {
        const codeExists = await checkWorkProductLibraryCodeExists(
          values.code, 
          undefined
        );
        if (codeExists) {
          throw new Error('交付物编码已存在');
        }
      }

      if (unref(isUpdate)) {
        await updateWorkProductLibrary(rowId.value, values);
      } else {
        await createWorkProductLibrary(values);
      }

      closeModal();
      emit('success', { isUpdate: unref(isUpdate), values: { ...values, id: rowId.value } });
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>
