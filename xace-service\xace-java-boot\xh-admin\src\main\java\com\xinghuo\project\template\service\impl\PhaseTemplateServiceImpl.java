package com.xinghuo.project.template.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.util.core.RandomUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.project.template.dao.PhaseTemplateMapper;
import com.xinghuo.project.template.entity.PhaseTemplateEntity;
import com.xinghuo.project.template.model.PhaseTemplatePagination;
import com.xinghuo.project.template.service.PhaseTemplateService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 阶段模板服务实现类
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Slf4j
@Service
public class PhaseTemplateServiceImpl extends BaseServiceImpl<PhaseTemplateMapper, PhaseTemplateEntity> implements PhaseTemplateService {

    @Resource
    private PhaseTemplateMapper phaseTemplateMapper;

    @Override
    public List<PhaseTemplateEntity> getList(PhaseTemplatePagination pagination) {
        QueryWrapper<PhaseTemplateEntity> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<PhaseTemplateEntity> lambda = queryWrapper.lambda();

        // 根据阶段编码模糊查询
        if (StrXhUtil.isNotEmpty(pagination.getCode())) {
            lambda.like(PhaseTemplateEntity::getCode, pagination.getCode());
        }

        // 根据阶段名称模糊查询
        if (StrXhUtil.isNotEmpty(pagination.getName())) {
            lambda.like(PhaseTemplateEntity::getName, pagination.getName());
        }

        // 根据状态精确查询
        if (pagination.getStatus() != null) {
            lambda.eq(PhaseTemplateEntity::getStatus, pagination.getStatus());
        }

        // 标准工期范围查询
        if (pagination.getStdDurationMin() != null) {
            lambda.ge(PhaseTemplateEntity::getStdDuration, pagination.getStdDurationMin());
        }
        if (pagination.getStdDurationMax() != null) {
            lambda.le(PhaseTemplateEntity::getStdDuration, pagination.getStdDurationMax());
        }

        // 创建时间范围查询
        if (pagination.getCreateTimeStart() != null) {
            lambda.ge(PhaseTemplateEntity::getCreatedAt, pagination.getCreateTimeStart());
        }
        if (pagination.getCreateTimeEnd() != null) {
            lambda.le(PhaseTemplateEntity::getCreatedAt, pagination.getCreateTimeEnd());
        }

        // 根据创建用户查询
//        if (StrXhUtil.isNotEmpty(pagination.getCreatorUserId())) {
//            lambda.eq(PhaseTemplateEntity::getCreatorUserId, pagination.getCreatorUserId());
//        }

        // 根据关键字搜索编码或名称
        String keyword = pagination.getKeyword();
        if (StrXhUtil.isNotEmpty(keyword)) {
            lambda.and(wrapper -> wrapper
                    .like(PhaseTemplateEntity::getCode, keyword)
                    .or()
                    .like(PhaseTemplateEntity::getName, keyword)
            );
        }

        // 排序：按创建时间倒序
        lambda.orderByDesc(PhaseTemplateEntity::getCreatedAt);
        
        return processDataType(queryWrapper, pagination);
    }

    @Override
    public List<PhaseTemplateEntity> getListByStatus(Integer status) {
        return phaseTemplateMapper.selectByStatus(status);
    }

    @Override
    public PhaseTemplateEntity getInfo(String id) {
        if (StrXhUtil.isEmpty(id)) {
            log.warn("查询阶段模板信息ID为空");
            return null;
        }
        return this.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String create(PhaseTemplateEntity entity) {
        if (entity == null) {
            log.warn("创建阶段模板信息为空");
            throw new RuntimeException("阶段模板信息不能为空");
        }

        // 验证必填字段
        if (StrXhUtil.isEmpty(entity.getName())) {
            log.warn("阶段模板名称不能为空");
            throw new RuntimeException("阶段模板名称不能为空");
        }

        // 检查阶段编码是否重复
        if (StrXhUtil.isNotEmpty(entity.getCode())) {
            boolean exists = isExistByCode(entity.getCode(), null);
            if (exists) {
                log.warn("阶段编码已存在: {}", entity.getCode());
                throw new RuntimeException("阶段编码已存在");
            }
        } else {
            // 如果没有提供编码，自动生成
            entity.setCode(generateCode());
        }

        // 设置ID
        String id = RandomUtil.snowId();
        entity.setId(id);

        // 设置默认状态
        if (entity.getStatus() == null) {
            entity.setStatus(1); // 默认启用
        }

        this.save(entity);
        log.info("创建阶段模板成功, ID: {}, 名称: {}", id, entity.getName());
        return id;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(String id, PhaseTemplateEntity entity) {
        if (StrXhUtil.isEmpty(id) || entity == null) {
            log.warn("更新阶段模板参数无效, ID: {}", id);
            throw new RuntimeException("更新参数无效");
        }

        // 查询原记录是否存在
        PhaseTemplateEntity dbEntity = this.getById(id);
        if (dbEntity == null) {
            log.warn("更新的阶段模板不存在, ID: {}", id);
            throw new RuntimeException("阶段模板不存在");
        }

        // 验证必填字段
        if (StrXhUtil.isEmpty(entity.getName())) {
            log.warn("阶段模板名称不能为空");
            throw new RuntimeException("阶段模板名称不能为空");
        }

        // 检查阶段编码是否重复（排除自身）
        if (StrXhUtil.isNotEmpty(entity.getCode())) {
            boolean exists = isExistByCode(entity.getCode(), id);
            if (exists) {
                log.warn("阶段编码已存在: {}", entity.getCode());
                throw new RuntimeException("阶段编码已存在");
            }
        }

        entity.setId(id);
        this.updateById(entity);
        log.info("更新阶段模板成功, ID: {}, 名称: {}", id, entity.getName());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        if (StrXhUtil.isEmpty(id)) {
            log.warn("删除阶段模板ID为空");
            throw new RuntimeException("阶段模板ID不能为空");
        }

        // 查询阶段模板是否存在
        PhaseTemplateEntity entity = this.getById(id);
        if (entity == null) {
            log.warn("删除的阶段模板不存在, ID: {}", id);
            throw new RuntimeException("阶段模板不存在");
        }

        this.removeById(id);
        log.info("删除阶段模板成功, ID: {}, 名称: {}", id, entity.getName());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDelete(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            log.warn("批量删除阶段模板ID列表为空");
            throw new RuntimeException("ID列表不能为空");
        }

        this.removeByIds(ids);
        log.info("批量删除阶段模板成功, 数量: {}", ids.size());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(String id, Integer status) {
        if (StrXhUtil.isEmpty(id) || status == null) {
            log.warn("更新阶段模板状态参数无效, ID: {}, status: {}", id, status);
            throw new RuntimeException("参数无效");
        }

        phaseTemplateMapper.updateStatus(id, status);
        log.info("更新阶段模板状态成功, ID: {}, status: {}", id, status);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateStatus(List<String> ids, Integer status) {
        if (ids == null || ids.isEmpty() || status == null) {
            log.warn("批量更新阶段模板状态参数无效");
            throw new RuntimeException("参数无效");
        }

        phaseTemplateMapper.batchUpdateStatus(ids, status);
        log.info("批量更新阶段模板状态成功, 数量: {}, status: {}", ids.size(), status);
    }

    @Override
    public boolean isExistByCode(String code, String excludeId) {
        if (StrXhUtil.isEmpty(code)) {
            return false;
        }
        int count = phaseTemplateMapper.checkCodeExists(code, excludeId);
        return count > 0;
    }

    @Override
    public PhaseTemplateEntity getByCode(String code) {
        if (StrXhUtil.isEmpty(code)) {
            return null;
        }
        return phaseTemplateMapper.selectByCode(code);
    }

    @Override
    public List<PhaseTemplateEntity> getSelectList(String keyword) {
        return phaseTemplateMapper.selectForSelect(keyword);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void enable(String id) {
        updateStatus(id, 1);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void disable(String id) {
        updateStatus(id, 0);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String copy(String id, String newName) {
        if (StrXhUtil.isEmpty(id) || StrXhUtil.isEmpty(newName)) {
            log.warn("复制阶段模板参数无效, ID: {}, newName: {}", id, newName);
            throw new RuntimeException("参数无效");
        }

        // 查询原阶段模板
        PhaseTemplateEntity sourceEntity = this.getById(id);
        if (sourceEntity == null) {
            log.warn("复制的阶段模板不存在, ID: {}", id);
            throw new RuntimeException("阶段模板不存在");
        }

        // 创建新的阶段模板
        PhaseTemplateEntity newEntity = new PhaseTemplateEntity();
        newEntity.setCode(generateCode());
        newEntity.setName(newName);
        newEntity.setDescription(sourceEntity.getDescription());
        newEntity.setStdDuration(sourceEntity.getStdDuration());
        newEntity.setDefaultApprovalId(sourceEntity.getDefaultApprovalId());
        newEntity.setDefaultChecklistId(sourceEntity.getDefaultChecklistId());
        newEntity.setStatus(0); // 复制的模板默认为禁用状态

        String newId = create(newEntity);
        log.info("复制阶段模板成功, 源ID: {}, 新ID: {}, 新名称: {}", id, newId, newName);
        return newId;
    }

    @Override
    public String generateCode() {
        // 获取下一个序号
        Integer sequence = phaseTemplateMapper.getNextCodeSequence();
        if (sequence == null) {
            sequence = 1;
        }
        
        // 生成编码：PL + 8位序号
        return String.format("PL%08d", sequence);
    }
}
