/**
 * Project Template Management Module - English Language Pack
 */
export default {
  // Common
  common: {
    create: 'Create',
    edit: 'Edit',
    delete: 'Delete',
    copy: 'Copy',
    view: 'View',
    detail: 'Detail',
    search: 'Search',
    reset: 'Reset',
    confirm: 'Confirm',
    cancel: 'Cancel',
    save: 'Save',
    submit: 'Submit',
    back: 'Back',
    close: 'Close',
    refresh: 'Refresh',
    export: 'Export',
    import: 'Import',
    batchDelete: 'Batch Delete',
    batchEnable: 'Batch Enable',
    batchDisable: 'Batch Disable',
    batchPublish: 'Batch Publish',
    batchArchive: 'Batch Archive',
    selectAll: 'Select All',
    selectNone: 'Select None',
    operation: 'Operation',
    status: 'Status',
    createTime: 'Create Time',
    updateTime: 'Update Time',
    creator: 'Creator',
    updater: 'Updater',
    remark: 'Remark',
    description: 'Description',
    name: 'Name',
    code: 'Code',
    type: 'Type',
    enabled: 'Enabled',
    disabled: 'Disabled',
    published: 'Published',
    draft: 'Draft',
    archived: 'Archived',
    yes: 'Yes',
    no: 'No',
    loading: 'Loading...',
    noData: 'No Data',
    total: 'Total {count} items',
  },

  // Phase Template
  phaseTemplate: {
    title: 'Phase Template',
    list: 'Phase Template List',
    create: 'Create Phase Template',
    edit: 'Edit Phase Template',
    copy: 'Copy Phase Template',
    detail: 'Phase Template Detail',
    fields: {
      code: 'Phase Code',
      name: 'Phase Name',
      description: 'Phase Description',
      stdDuration: 'Standard Duration',
      status: 'Status',
      defaultApproval: 'Default Approval Process',
      defaultChecklist: 'Default Checklist Template',
      usageCount: 'Usage Count',
      projectTemplateCount: 'Related Project Template Count',
    },
    placeholders: {
      code: 'Please enter phase code',
      name: 'Please enter phase name',
      description: 'Please enter phase description',
      stdDuration: 'Please enter standard duration (days)',
      searchKeyword: 'Please enter phase name or code',
    },
    messages: {
      createSuccess: 'Phase template created successfully',
      updateSuccess: 'Phase template updated successfully',
      deleteSuccess: 'Phase template deleted successfully',
      copySuccess: 'Phase template copied successfully',
      enableSuccess: 'Phase template enabled successfully',
      disableSuccess: 'Phase template disabled successfully',
      batchDeleteSuccess: 'Batch delete successful',
      batchEnableSuccess: 'Batch enable successful',
      batchDisableSuccess: 'Batch disable successful',
      deleteConfirm: 'Are you sure to delete phase template "{name}"?',
      batchDeleteConfirm: 'Are you sure to delete {count} selected phase templates?',
      selectRecords: 'Please select records to operate',
    },
  },

  // Phase Plan Template
  phasePlanTemplate: {
    title: 'Phase Plan Template',
    list: 'Phase Plan Template List',
    create: 'Create Phase Plan Template',
    edit: 'Edit Phase Plan Template',
    copy: 'Copy Phase Plan Template',
    detail: 'Phase Plan Template Detail',
    fields: {
      name: 'Template Name',
      description: 'Template Description',
      knStatus: 'Knowledge Status',
      phaseCount: 'Total Phases',
      totalDuration: 'Total Duration',
      projectTemplateCount: 'Related Project Template Count',
      usageCount: 'Usage Count',
      phases: 'Phase Details',
      relatedProjects: 'Related Project Templates',
    },
    placeholders: {
      name: 'Please enter template name',
      description: 'Please enter template description',
      searchKeyword: 'Please enter template name',
    },
    messages: {
      createSuccess: 'Phase plan template created successfully',
      updateSuccess: 'Phase plan template updated successfully',
      deleteSuccess: 'Phase plan template deleted successfully',
      copySuccess: 'Phase plan template copied successfully',
      publishSuccess: 'Phase plan template published successfully',
      archiveSuccess: 'Phase plan template archived successfully',
      applySuccess: 'Template applied successfully',
    },
  },

  // Work Product Library
  workProductLibrary: {
    title: 'Work Product Library',
    list: 'Work Product Library List',
    create: 'Create Work Product',
    edit: 'Edit Work Product',
    copy: 'Copy Work Product',
    detail: 'Work Product Detail',
    fields: {
      code: 'Work Product Code',
      name: 'Work Product Name',
      description: 'Work Product Description',
      type: 'Work Product Type',
      subType: 'Work Product Sub Type',
      defaultRole: 'Default Responsible Role',
      needReview: 'Need Review',
      isDeliverable: 'Final Deliverable',
      canCut: 'Can Cut',
      status: 'Status',
      usageCount: 'Usage Count',
      projectTemplateCount: 'Related Project Template Count',
    },
    placeholders: {
      code: 'Please enter work product code (auto-generated)',
      name: 'Please enter work product name',
      description: 'Please enter work product description',
      searchKeyword: 'Please enter work product name or code',
    },
    messages: {
      createSuccess: 'Work product created successfully',
      updateSuccess: 'Work product updated successfully',
      deleteSuccess: 'Work product deleted successfully',
      copySuccess: 'Work product copied successfully',
      codeGenerated: 'Code auto-generated',
    },
  },

  // Work Product Plan Template
  workProductPlanTemplate: {
    title: 'Work Product Plan Template',
    list: 'Work Product Plan Template List',
    create: 'Create Work Product Plan Template',
    edit: 'Edit Work Product Plan Template',
    copy: 'Copy Work Product Plan Template',
    detail: 'Work Product Plan Template Detail',
    fields: {
      name: 'Template Name',
      description: 'Template Description',
      knStatus: 'Knowledge Status',
      workProductCount: 'Total Work Products',
      reviewRequiredCount: 'Review Required Count',
      deliverableCount: 'Final Deliverable Count',
      projectTemplateCount: 'Related Project Template Count',
      workProducts: 'Work Product Details',
    },
    placeholders: {
      name: 'Please enter template name',
      description: 'Please enter template description',
      searchKeyword: 'Please enter template name',
    },
    messages: {
      createSuccess: 'Work product plan template created successfully',
      updateSuccess: 'Work product plan template updated successfully',
      deleteSuccess: 'Work product plan template deleted successfully',
      copySuccess: 'Work product plan template copied successfully',
      publishSuccess: 'Work product plan template published successfully',
      archiveSuccess: 'Work product plan template archived successfully',
    },
  },

  // Usage Info
  usageInfo: {
    title: 'Usage Statistics',
    basicStats: 'Basic Statistics',
    usageTrend: 'Usage Trend',
    relatedProjects: 'Related Projects',
    recentUsage: 'Recent Usage',
    fields: {
      totalUsage: 'Total Usage',
      projectCount: 'Project Count',
      activeProjectCount: 'Active Project Count',
      period: 'Period',
      count: 'Count',
      newProjects: 'New Projects',
      activeProjects: 'Active Projects',
      projectName: 'Project Name',
      projectCode: 'Project Code',
      projectStatus: 'Project Status',
      usedAt: 'Used At',
      usedBy: 'Used By',
      actionType: 'Action Type',
      actionTime: 'Action Time',
      actionBy: 'Action By',
    },
    messages: {
      noUsageData: 'No usage data',
    },
  },

  // Apply to Project
  applyToProject: {
    title: 'Apply Template to Project',
    templateInfo: 'Template Information',
    projectSelection: 'Select Target Project',
    applyOptions: 'Apply Options',
    preview: 'Apply Preview',
    fields: {
      templateName: 'Template Name',
      templateType: 'Template Type',
      targetProject: 'Target Project',
      overwrite: 'Overwrite existing configuration',
      backup: 'Backup before applying',
      notify: 'Notify project members after applying',
      remark: 'Apply Remark',
      phaseCount: 'Phases to add',
      activityCount: 'Activities to add',
      workProductCount: 'Work products to add',
      totalDuration: 'Estimated total duration',
    },
    placeholders: {
      targetProject: 'Please select target project',
      remark: 'Please enter apply remark (optional)',
    },
    messages: {
      applySuccess: 'Template applied successfully',
      selectProject: 'Please select target project',
      previewInfo: 'Will apply template "{templateName}" to project "{projectName}"',
    },
  },

  // Validation Messages
  validation: {
    required: 'This field is required',
    codeFormat: 'Code format is incorrect (start with letter, can contain letters, numbers, underscores, length 3-20)',
    nameFormat: 'Name format is incorrect (cannot contain special characters)',
    durationRange: 'Duration must be a positive integer between 1-9999',
    maxLength: 'Length cannot exceed {max} characters',
    minLength: 'Length cannot be less than {min} characters',
    emailFormat: 'Email format is incorrect',
    phoneFormat: 'Phone format is incorrect',
    urlFormat: 'URL format is incorrect',
    numberRange: 'Value must be between {min}-{max}',
    nameExists: 'Name already exists',
    codeExists: 'Code already exists',
  },

  // Error Messages
  errors: {
    networkError: 'Network connection failed, please check network settings',
    serverError: 'Server error, please try again later',
    permissionDenied: 'Permission denied, cannot perform this operation',
    dataNotFound: 'Data not found or has been deleted',
    operationFailed: 'Operation failed, please try again',
    validationFailed: 'Data validation failed',
    timeout: 'Operation timeout, please try again',
  },
};
