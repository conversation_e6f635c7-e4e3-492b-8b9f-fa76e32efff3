package com.xinghuo.project.core.controller;

import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.project.core.entity.TagEntity;
import com.xinghuo.project.core.model.tag.TagPagination;
import com.xinghuo.project.core.service.TagService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 标签管理控制器
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Slf4j
@Tag(name = "标签管理", description = "标签管理相关接口")
@RestController
@RequestMapping("/api/project/core/tag")
public class TagController {

    @Resource
    private TagService tagService;

    /**
     * 获取标签列表
     */
    @PostMapping("/getList")
    @Operation(summary = "获取标签列表")
    public ActionResult<PageListVO<TagEntity>> list(@RequestBody TagPagination pagination) {
        List<TagEntity> list = tagService.getList(pagination);
        PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
        return ActionResult.page(list, page);
    }

    /**
     * 获取标签详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取标签详情")
    public ActionResult<TagEntity> getInfo(
            @Parameter(description = "标签ID") @PathVariable String id) {
        TagEntity entity = tagService.getInfo(id);
        return ActionResult.success(entity);
    }

    /**
     * 创建标签
     */
    @PostMapping("/create")
    @Operation(summary = "创建标签")
    public ActionResult<String> create(@RequestBody @Valid TagEntity entity) {
        String id = tagService.create(entity);
        return ActionResult.success("创建成功", id);
    }

    /**
     * 更新标签
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新标签")
    public ActionResult<String> update(
            @Parameter(description = "标签ID") @PathVariable String id,
            @RequestBody @Valid TagEntity entity) {
        tagService.update(id, entity);
        return ActionResult.success("更新成功");
    }

    /**
     * 删除标签
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除标签")
    public ActionResult<String> delete(
            @Parameter(description = "标签ID") @PathVariable String id) {
        tagService.delete(id);
        return ActionResult.success("删除成功");
    }

    /**
     * 检查标签名称是否存在
     */
    @GetMapping("/checkNameExists")
    @Operation(summary = "检查标签名称是否存在")
    public ActionResult<Boolean> checkNameExists(
            @RequestParam String tagName,
            @RequestParam(required = false) String excludeId) {
        boolean exists = tagService.isExistByTagName(tagName, excludeId);
        return ActionResult.success(exists);
    }

    /**
     * 获取标签选择列表
     */
    @GetMapping("/getSelectList")
    @Operation(summary = "获取标签选择列表")
    public ActionResult<List<TagEntity>> getSelectList(
            @RequestParam(required = false) String keyword) {
        List<TagEntity> list = tagService.getSelectList(keyword);
        return ActionResult.success(list);
    }
}
