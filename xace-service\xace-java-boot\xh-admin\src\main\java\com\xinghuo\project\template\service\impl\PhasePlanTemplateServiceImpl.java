package com.xinghuo.project.template.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.util.core.RandomUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.project.template.dao.PhasePlanTemplateDetailMapper;
import com.xinghuo.project.template.dao.PhasePlanTemplateMapper;
import com.xinghuo.project.template.entity.PhasePlanTemplateDetailEntity;
import com.xinghuo.project.template.entity.PhasePlanTemplateEntity;
import com.xinghuo.project.template.enums.TemplateRelationTypeEnum;
import com.xinghuo.project.template.model.PhasePlanTemplatePagination;
import com.xinghuo.project.template.model.dto.PhasePlanTemplateDTO;
import com.xinghuo.project.template.service.PhasePlanTemplateService;
import com.xinghuo.project.template.service.TemplateRelationService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 阶段计划模板服务实现类
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Slf4j
@Service
public class PhasePlanTemplateServiceImpl extends BaseServiceImpl<PhasePlanTemplateMapper, PhasePlanTemplateEntity> implements PhasePlanTemplateService {

    @Resource
    private PhasePlanTemplateMapper phasePlanTemplateMapper;

    @Resource
    private PhasePlanTemplateDetailMapper phasePlanTemplateDetailMapper;

    @Resource
    private TemplateRelationService templateRelationService;

    @Override
    public List<PhasePlanTemplateDTO> getList(PhasePlanTemplatePagination pagination) {
        QueryWrapper<PhasePlanTemplateEntity> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<PhasePlanTemplateEntity> lambda = queryWrapper.lambda();

        // 根据模板名称模糊查询
        if (StrXhUtil.isNotEmpty(pagination.getName())) {
            lambda.like(PhasePlanTemplateEntity::getName, pagination.getName());
        }

        // 根据知识状态精确查询
        if (StrXhUtil.isNotEmpty(pagination.getKnStatusId())) {
            lambda.eq(PhasePlanTemplateEntity::getKnStatusId, pagination.getKnStatusId());
        }

        // 创建时间范围查询
        if (pagination.getCreateTimeStart() != null) {
            lambda.ge(PhasePlanTemplateEntity::getCreatedAt, pagination.getCreateTimeStart());
        }
        if (pagination.getCreateTimeEnd() != null) {
            lambda.le(PhasePlanTemplateEntity::getCreatedAt, pagination.getCreateTimeEnd());
        }

        // 根据创建用户查询
//        if (StrXhUtil.isNotEmpty(pagination.getCreatorUserId())) {
//            lambda.eq(PhasePlanTemplateEntity::getCreatorUserId, pagination.getCreatorUserId());
//        }

        // 根据关键字搜索名称或描述
        String keyword = pagination.getKeyword();
        if (StrXhUtil.isNotEmpty(keyword)) {
            lambda.and(wrapper -> wrapper
                    .like(PhasePlanTemplateEntity::getName, keyword)
                    .or()
                    .like(PhasePlanTemplateEntity::getDescription, keyword)
            );
        }

        // 排序：按创建时间倒序
        lambda.orderByDesc(PhasePlanTemplateEntity::getCreatedAt);
        
        List<PhasePlanTemplateEntity> entities = processDataType(queryWrapper, pagination);
        
        // 转换为DTO并填充统计信息
        return entities.stream().map(this::convertToDTO).collect(Collectors.toList());
    }

    @Override
    public List<PhasePlanTemplateEntity> getListByKnStatus(String knStatusId) {
        return phasePlanTemplateMapper.selectByKnStatus(knStatusId);
    }

    @Override
    public PhasePlanTemplateDTO getDetailInfo(String id) {
        if (StrXhUtil.isEmpty(id)) {
            log.warn("查询模板详情ID为空");
            return null;
        }
        return phasePlanTemplateMapper.selectDetailById(id);
    }

    @Override
    public PhasePlanTemplateEntity getInfo(String id) {
        if (StrXhUtil.isEmpty(id)) {
            log.warn("查询模板信息ID为空");
            return null;
        }
        return this.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String create(PhasePlanTemplateDTO templateDTO) {
        if (templateDTO == null) {
            log.warn("创建模板信息为空");
            throw new RuntimeException("模板信息不能为空");
        }

        // 验证必填字段
        if (StrXhUtil.isEmpty(templateDTO.getName())) {
            log.warn("模板名称不能为空");
            throw new RuntimeException("模板名称不能为空");
        }

        // 检查模板名称是否重复
        boolean exists = isExistByName(templateDTO.getName(), null);
        if (exists) {
            log.warn("模板名称已存在: {}", templateDTO.getName());
            throw new RuntimeException("模板名称已存在");
        }

        // 创建主表记录
        String id = RandomUtil.snowId();
        templateDTO.setId(id);
        
        // 设置默认知识状态为"未发布"
        if (StrXhUtil.isEmpty(templateDTO.getKnStatusId())) {
            templateDTO.setKnStatusId("draft"); // 假设draft为未发布状态
        }

        this.save(templateDTO);

        // 保存阶段明细
        if (templateDTO.getPhaseDetails() != null && !templateDTO.getPhaseDetails().isEmpty()) {
            savePhaseDetails(id, templateDTO.getPhaseDetails());
        }

        // 保存项目模板关联关系
        if (templateDTO.getProjectTemplateIds() != null && !templateDTO.getProjectTemplateIds().isEmpty()) {
            templateRelationService.updateTemplateRelations(id, TemplateRelationTypeEnum.PHASE_TEMPLATE.getCode(),
                    templateDTO.getProjectTemplateIds(), TemplateRelationTypeEnum.PROJECT_TEMPLATE.getCode());
        }

        log.info("创建阶段计划模板成功, ID: {}, 名称: {}", id, templateDTO.getName());
        return id;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(String id, PhasePlanTemplateDTO templateDTO) {
        if (StrXhUtil.isEmpty(id) || templateDTO == null) {
            log.warn("更新模板参数无效, ID: {}", id);
            throw new RuntimeException("更新参数无效");
        }

        // 查询原记录是否存在
        PhasePlanTemplateEntity dbEntity = this.getById(id);
        if (dbEntity == null) {
            log.warn("更新的模板不存在, ID: {}", id);
            throw new RuntimeException("模板不存在");
        }

        // 验证必填字段
        if (StrXhUtil.isEmpty(templateDTO.getName())) {
            log.warn("模板名称不能为空");
            throw new RuntimeException("模板名称不能为空");
        }

        // 检查模板名称是否重复（排除自身）
        boolean exists = isExistByName(templateDTO.getName(), id);
        if (exists) {
            log.warn("模板名称已存在: {}", templateDTO.getName());
            throw new RuntimeException("模板名称已存在");
        }

        // 更新主表
        templateDTO.setId(id);
        this.updateById(templateDTO);

        // 删除原有阶段明细，重新保存
        phasePlanTemplateDetailMapper.deleteByTemplateId(id);
        if (templateDTO.getPhaseDetails() != null && !templateDTO.getPhaseDetails().isEmpty()) {
            savePhaseDetails(id, templateDTO.getPhaseDetails());
        }

        // 删除原有项目模板关联关系，重新保存
        templateRelationService.updateTemplateRelations(id, TemplateRelationTypeEnum.PHASE_TEMPLATE.getCode(),
                templateDTO.getProjectTemplateIds(), TemplateRelationTypeEnum.PROJECT_TEMPLATE.getCode());

        log.info("更新阶段计划模板成功, ID: {}, 名称: {}", id, templateDTO.getName());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        if (StrXhUtil.isEmpty(id)) {
            log.warn("删除模板ID为空");
            throw new RuntimeException("模板ID不能为空");
        }

        // 查询模板是否存在
        PhasePlanTemplateEntity entity = this.getById(id);
        if (entity == null) {
            log.warn("删除的模板不存在, ID: {}", id);
            throw new RuntimeException("模板不存在");
        }

        // 检查模板是否正在使用
        Map<String, Object> usageInfo = getTemplateUsageInfo(id);
        Integer usageCount = (Integer) usageInfo.get("usageCount");
        if (usageCount != null && usageCount > 0) {
            log.warn("模板正在使用中，不能删除, ID: {}", id);
            throw new RuntimeException("模板正在使用中，不能删除");
        }

        // 删除关联数据
        phasePlanTemplateDetailMapper.deleteByTemplateId(id);
        templateRelationService.deleteBySourceTemplate(id, TemplateRelationTypeEnum.PHASE_TEMPLATE.getCode());
        
        // 删除主表
        this.removeById(id);
        log.info("删除阶段计划模板成功, ID: {}, 名称: {}", id, entity.getName());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDelete(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            log.warn("批量删除模板ID列表为空");
            throw new RuntimeException("ID列表不能为空");
        }

        for (String id : ids) {
            delete(id);
        }
        log.info("批量删除阶段计划模板成功, 数量: {}", ids.size());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateKnStatus(String id, String knStatusId) {
        if (StrXhUtil.isEmpty(id) || StrXhUtil.isEmpty(knStatusId)) {
            log.warn("更新模板知识状态参数无效, ID: {}, knStatusId: {}", id, knStatusId);
            throw new RuntimeException("参数无效");
        }

        phasePlanTemplateMapper.updateKnStatus(id, knStatusId);
        log.info("更新模板知识状态成功, ID: {}, knStatusId: {}", id, knStatusId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateKnStatus(List<String> ids, String knStatusId) {
        if (ids == null || ids.isEmpty() || StrXhUtil.isEmpty(knStatusId)) {
            log.warn("批量更新模板知识状态参数无效");
            throw new RuntimeException("参数无效");
        }

        phasePlanTemplateMapper.batchUpdateKnStatus(ids, knStatusId);
        log.info("批量更新模板知识状态成功, 数量: {}, knStatusId: {}", ids.size(), knStatusId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void publish(String id) {
        updateKnStatus(id, "published"); // 假设published为已发布状态
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void archive(String id) {
        updateKnStatus(id, "archived"); // 假设archived为已归档状态
    }

    @Override
    public boolean isExistByName(String name, String excludeId) {
        if (StrXhUtil.isEmpty(name)) {
            return false;
        }
        int count = phasePlanTemplateMapper.checkNameExists(name, excludeId);
        return count > 0;
    }

    @Override
    public List<PhasePlanTemplateDTO> getSelectList(String keyword, String knStatusId) {
        return phasePlanTemplateMapper.selectForSelect(keyword, knStatusId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String copy(String id, String newName) {
        if (StrXhUtil.isEmpty(id) || StrXhUtil.isEmpty(newName)) {
            log.warn("复制模板参数无效, ID: {}, newName: {}", id, newName);
            throw new RuntimeException("参数无效");
        }

        // 查询原模板详情
        PhasePlanTemplateDTO sourceTemplate = getDetailInfo(id);
        if (sourceTemplate == null) {
            log.warn("复制的模板不存在, ID: {}", id);
            throw new RuntimeException("模板不存在");
        }

        // 创建新模板
        PhasePlanTemplateDTO newTemplate = new PhasePlanTemplateDTO();
        newTemplate.setName(newName);
        newTemplate.setDescription(sourceTemplate.getDescription());
        newTemplate.setKnStatusId("draft"); // 复制的模板默认为草稿状态
        newTemplate.setPhaseDetails(sourceTemplate.getPhaseDetails());
        newTemplate.setProjectTemplateIds(sourceTemplate.getProjectTemplateIds());

        String newId = create(newTemplate);
        log.info("复制阶段计划模板成功, 源ID: {}, 新ID: {}, 新名称: {}", id, newId, newName);
        return newId;
    }

    @Override
    public List<PhasePlanTemplateDetailEntity> addPhasesFromStandardLibrary(String templateId, List<String> phaseCodes) {
        // 这里需要实现从标准阶段库添加阶段的逻辑
        // 暂时返回空列表，具体实现需要根据标准阶段库的设计
        return new ArrayList<>();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePhaseOrder(String templateId, List<Map<String, Object>> phaseOrders) {
        if (StrXhUtil.isEmpty(templateId) || phaseOrders == null || phaseOrders.isEmpty()) {
            log.warn("更新阶段顺序参数无效");
            throw new RuntimeException("参数无效");
        }

        List<PhasePlanTemplateDetailEntity> details = new ArrayList<>();
        for (Map<String, Object> order : phaseOrders) {
            PhasePlanTemplateDetailEntity detail = new PhasePlanTemplateDetailEntity();
            detail.setId((String) order.get("id"));
            detail.setSeqNo((Integer) order.get("seqNo"));
            details.add(detail);
        }

        phasePlanTemplateDetailMapper.batchUpdateSeqNo(details);
        log.info("更新阶段顺序成功, 模板ID: {}", templateId);
    }

    @Override
    public List<Map<String, Object>> getTemplateStatistics(Map<String, Object> params) {
        return phasePlanTemplateMapper.getTemplateStatistics(params);
    }

    @Override
    public List<PhasePlanTemplateEntity> getByProjectTemplateId(String projectTplId) {
        // 先通过通用关联表查询关联的阶段计划模板ID列表
        List<String> templateIds = templateRelationService.getSourceTemplateIds(projectTplId,
                TemplateRelationTypeEnum.PROJECT_TEMPLATE.getCode(),
                TemplateRelationTypeEnum.PHASE_TEMPLATE.getCode());

        if (templateIds.isEmpty()) {
            return new ArrayList<>();
        }

        // 根据ID列表查询模板详情
        return this.listByIds(templateIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProjectTemplateRelations(String templateId, List<String> projectTemplateIds) {
        if (StrXhUtil.isEmpty(templateId)) {
            log.warn("更新项目模板关联关系，模板ID为空");
            throw new RuntimeException("模板ID不能为空");
        }

        // 更新项目模板关联关系
        templateRelationService.updateTemplateRelations(templateId, TemplateRelationTypeEnum.PHASE_TEMPLATE.getCode(),
                projectTemplateIds, TemplateRelationTypeEnum.PROJECT_TEMPLATE.getCode());

        log.info("更新项目模板关联关系成功, 模板ID: {}", templateId);
    }

    @Override
    public String createFromProject(String projectId, String templateName, String description) {
        // 这里需要实现从项目创建模板的逻辑
        // 暂时抛出异常，具体实现需要根据项目管理模块的设计
        throw new RuntimeException("从项目创建模板功能待实现");
    }

    @Override
    public Map<String, Object> applyToProject(String templateId, String projectId) {
        // 这里需要实现应用模板到项目的逻辑
        // 暂时抛出异常，具体实现需要根据项目管理模块的设计
        throw new RuntimeException("应用模板到项目功能待实现");
    }

    @Override
    public Map<String, Object> getTemplateUsageInfo(String id) {
        return phasePlanTemplateMapper.getTemplateUsageInfo(id);
    }

    /**
     * 转换实体为DTO
     */
    private PhasePlanTemplateDTO convertToDTO(PhasePlanTemplateEntity entity) {
        PhasePlanTemplateDTO dto = new PhasePlanTemplateDTO();
        dto.setId(entity.getId());
        dto.setName(entity.getName());
        dto.setDescription(entity.getDescription());
        dto.setKnStatusId(entity.getKnStatusId());
        dto.setCreatedAt(entity.getCreatedAt());
        // dto.setCreatorUserId(entity.getCreatorUserId()); // 需要根据实际字段调整
        
        // 查询阶段明细统计信息
        List<PhasePlanTemplateDetailEntity> details = phasePlanTemplateDetailMapper.selectByTemplateId(entity.getId());
        dto.setPhaseCount(details.size());
        dto.setTotalDuration(details.stream().mapToInt(d -> d.getDuration() != null ? d.getDuration() : 0).sum());
        
        return dto;
    }

    /**
     * 保存阶段明细
     */
    private void savePhaseDetails(String templateId, List<PhasePlanTemplateDetailEntity> phaseDetails) {
        for (int i = 0; i < phaseDetails.size(); i++) {
            PhasePlanTemplateDetailEntity detail = phaseDetails.get(i);
            detail.setId(RandomUtil.snowId());
            detail.setPhasePlanTplId(templateId);
            detail.setSeqNo(i + 1);
            if (detail.getDuration() == null) {
                detail.setDuration(1);
            }
            if (detail.getCanCut() == null) {
                detail.setCanCut(1);
            }
        }
        phasePlanTemplateDetailMapper.batchInsert(phaseDetails);
    }


}
