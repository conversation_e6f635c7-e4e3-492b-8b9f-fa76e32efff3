package com.xinghuo.project.core.dao;

import com.xinghuo.common.base.dao.XHBaseMapper;
import com.xinghuo.project.core.entity.ProjectBaseEntity;
import com.xinghuo.project.core.model.dto.ProjectExtendedDTO;
import com.xinghuo.project.core.model.dto.SimpleProjectInfoDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 项目Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Mapper
public interface ProjectBaseMapper extends XHBaseMapper<ProjectBaseEntity> {

    /**
     * 根据项目类型查询项目列表
     *
     * @param projectType 项目类型
     * @return 项目列表
     */
    List<ProjectBaseEntity> selectByProjectType(@Param("projectType") String projectType);

    /**
     * 根据项目状态查询项目列表
     *
     * @param status 项目状态
     * @return 项目列表
     */
    List<ProjectBaseEntity> selectByStatus(@Param("status") String status);

    /**
     * 根据项目经理ID查询项目列表
     *
     * @param managerId 项目经理ID
     * @return 项目列表
     */
    List<ProjectBaseEntity> selectByManagerId(@Param("managerId") String managerId);

    /**
     * 根据部门ID查询项目列表
     *
     * @param deptId 部门ID
     * @return 项目列表
     */
    List<ProjectBaseEntity> selectByDeptId(@Param("deptId") String deptId);

    /**
     * 检查项目编码是否存在
     *
     * @param code 项目编码
     * @param excludeId 排除的ID
     * @return 数量
     */
    int checkCodeExists(@Param("code") String code, @Param("excludeId") String excludeId);

    /**
     * 获取项目统计数据
     *
     * @param params 查询参数
     * @return 统计数据
     */
    List<Map<String, Object>> getProjectStatistics(@Param("params") Map<String, Object> params);

    /**
     * 获取项目健康度统计
     *
     * @param params 查询参数
     * @return 健康度统计
     */
    List<Map<String, Object>> getProjectHealthStatistics(@Param("params") Map<String, Object> params);

    /**
     * 获取项目简要信息
     *
     * @param projectId 项目ID
     * @return 项目简要信息
     */
    SimpleProjectInfoDTO selectSimpleProjectInfo(@Param("projectId") String projectId);

    /**
     * 获取项目扩展信息
     *
     * @param projectId 项目ID
     * @return 项目扩展信息
     */
    ProjectExtendedDTO selectExtendedProjectInfo(@Param("projectId") String projectId);

    /**
     * 项目搜索
     *
     * @param searchParams 搜索参数
     * @return 项目列表
     */
    List<ProjectExtendedDTO> searchProjects(@Param("searchParams") Map<String, Object> searchParams);

    /**
     * 根据用户ID查询参与的项目
     *
     * @param userId 用户ID
     * @param params 查询参数
     * @return 项目列表
     */
    List<ProjectExtendedDTO> selectParticipatedProjects(@Param("userId") String userId, @Param("params") Map<String, Object> params);

    /**
     * 根据用户ID查询管理的项目
     *
     * @param userId 用户ID
     * @param params 查询参数
     * @return 项目列表
     */
    List<ProjectExtendedDTO> selectManagedProjects(@Param("userId") String userId, @Param("params") Map<String, Object> params);

    /**
     * 根据项目群ID查询项目列表
     *
     * @param programId 项目群ID
     * @return 项目列表
     */
    List<ProjectBaseEntity> selectByProgramId(@Param("programId") String programId);

    /**
     * 根据客户ID查询项目列表
     *
     * @param customerId 客户ID
     * @return 项目列表
     */
    List<ProjectBaseEntity> selectByCustomerId(@Param("customerId") String customerId);

    /**
     * 根据合同ID查询项目列表
     *
     * @param contractId 合同ID
     * @return 项目列表
     */
    List<ProjectBaseEntity> selectByContractId(@Param("contractId") String contractId);

    /**
     * 更新项目状态
     *
     * @param id 项目ID
     * @param status 项目状态
     * @return 更新数量
     */
    int updateStatus(@Param("id") String id, @Param("status") String status);

    /**
     * 更新项目健康度
     *
     * @param id 项目ID
     * @param health 项目健康度
     * @return 更新数量
     */
    int updateHealth(@Param("id") String id, @Param("health") String health);

    /**
     * 批量更新项目状态
     *
     * @param ids 项目ID列表
     * @param status 项目状态
     * @return 更新数量
     */
    int batchUpdateStatus(@Param("ids") List<String> ids, @Param("status") String status);

    /**
     * 获取项目数量统计
     *
     * @param params 查询参数
     * @return 统计结果
     */
    Map<String, Object> getProjectCountStatistics(@Param("params") Map<String, Object> params);

    /**
     * 获取项目进度统计
     *
     * @param params 查询参数
     * @return 统计结果
     */
    List<Map<String, Object>> getProjectProgressStatistics(@Param("params") Map<String, Object> params);

    /**
     * 根据关键字搜索项目
     *
     * @param keyword 关键字
     * @param limit 限制数量
     * @return 项目列表
     */
    List<ProjectBaseEntity> searchByKeyword(@Param("keyword") String keyword, @Param("limit") Integer limit);
}
